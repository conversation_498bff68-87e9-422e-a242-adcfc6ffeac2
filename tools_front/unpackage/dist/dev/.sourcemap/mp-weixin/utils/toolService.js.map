{"version": 3, "file": "toolService.js", "sources": ["utils/toolService.js"], "sourcesContent": ["// 工具服务类\nimport { showLoading, hideLoading, showSuccess, showError } from './index.js'\nimport { authManager } from './authManager.js'\n\n// API基础URL配置\nconst BASE_URL = 'http://localhost:8080'\n\n/**\n * 工具服务类\n */\nexport class ToolService {\n  constructor() {\n    this.baseUrl = BASE_URL\n  }\n\n  /**\n   * 执行工具\n   * @param {string} toolIdentifier 工具标识符\n   * @param {object} params 工具参数\n   * @returns {Promise} 执行结果\n   */\n  async executeTool(toolIdentifier, params = {}) {\n    try {\n      showLoading('处理中...')\n      \n      // 尝试无感登录\n      await this.ensureAuthenticated()\n      \n      const result = await this.callToolAPI(`/api/tools/${toolIdentifier}`, 'POST', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 确保用户已认证\n   */\n  async ensureAuthenticated() {\n    try {\n      // 如果已经有token，直接返回\n      if (authManager.isLoggedIn()) {\n        return true\n      }\n\n      // 尝试无感登录\n      console.log('尝试无感登录...')\n      const loginSuccess = await authManager.silentLogin()\n      \n      if (!loginSuccess) {\n        console.warn('无感登录失败，但继续执行（工具接口已开放）')\n      }\n      \n      return true\n    } catch (error) {\n      console.error('认证失败:', error)\n      // 即使认证失败，也继续执行，因为工具接口已经开放\n      return true\n    }\n  }\n\n  // ============ 好玩推荐工具 ============\n\n  /**\n   * 趣味图片生成器\n   */\n  async generateFunImage(params) {\n    try {\n      showLoading('生成中...')\n      \n      // 尝试无感登录\n      await this.ensureAuthenticated()\n      \n      const result = await this.callToolAPI('/api/tools/fun/fun-image-generator', 'POST', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 获取永久会员\n   */\n  async getVipMembership(params) {\n    try {\n      showLoading('处理中...')\n      const result = await this.callToolAPI('/api/tools/fun/get-vip-membership', 'POST', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  // ============ 媒体工具 ============\n\n  /**\n   * 视频解析去水印\n   */\n  async removeVideoWatermark(videoUrl) {\n    try {\n      showLoading('解析中...')\n      const result = await this.callToolAPI('/api/tools/media/video-watermark-remover', 'POST', { videoUrl })\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 图集解析去水印\n   */\n  async removeImageWatermark(imageUrls) {\n    try {\n      showLoading('处理中...')\n      const result = await this.callToolAPI('/api/tools/media/image-watermark-remover', 'POST', { imageUrls })\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 视频下载\n   */\n  async downloadVideo(params) {\n    try {\n      showLoading('解析中...')\n      // 确保参数格式正确\n      const requestParams = {\n        videoUrl: params.videoUrl || params.url, // 优先使用videoUrl参数\n        quality: params.quality || 'high',\n        format: params.format || 'mp4'\n      }\n      \n      console.log('发送视频解析请求:', requestParams)\n      const result = await this.callToolAPI('/api/tools/media/video-downloader', 'POST', requestParams)\n      console.log('视频解析返回结果:', result)\n      \n      hideLoading()\n      return result\n    } catch (error) {\n      console.error('视频解析失败:', error)\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 下载视频文件到本地\n   */\n  async downloadVideoFile(params) {\n    try {\n      showLoading('下载中...')\n      \n      // 构建文件名，确保以.mp4结尾\n      let fileName = params.fileName || `video_${Date.now()}.mp4`\n      \n      // 确保文件名以.mp4结尾\n      if (!fileName.toLowerCase().endsWith('.mp4')) {\n        // 移除现有扩展名（如果有）\n        const lastDotIndex = fileName.lastIndexOf('.')\n        if (lastDotIndex > 0) {\n          fileName = fileName.substring(0, lastDotIndex)\n        }\n        fileName += '.mp4'\n      }\n      \n      // 清理文件名中的特殊字符\n      fileName = fileName.replace(/[/\\\\:*?\"<>|]/g, '_')\n      \n      const requestParams = {\n        videoUrl: params.videoUrl,\n        fileName: fileName,\n        quality: params.quality\n      }\n      \n      // 检查baseUrl是否存在\n      if (!this.baseUrl) {\n        console.error('baseUrl未定义')\n        throw new Error('服务器地址未配置')\n      }\n      \n      // #ifdef MP-WEIXIN\n      try {\n        // 微信小程序使用GET请求，通过URL参数传递\n        const queryString = Object.keys(requestParams)\n          .filter(key => requestParams[key] !== undefined && requestParams[key] !== null)\n          .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(requestParams[key])}`)\n          .join('&')\n        \n        const downloadUrl = `${this.baseUrl}/api/tools/media/download?${queryString}`\n        console.log('下载请求URL:', downloadUrl)\n        \n        // 直接使用downloadFile API下载\n        const downloadRes = await uni.downloadFile({\n          url: downloadUrl,\n          header: {\n            'Accept': 'video/mp4, */*'\n          }\n        })\n        \n        console.log('下载结果:', downloadRes)\n        \n        if (downloadRes.statusCode === 200) {\n          // 保存到相册\n          await uni.saveVideoToPhotosAlbum({\n            filePath: downloadRes.tempFilePath\n          })\n          \n          uni.showToast({\n            title: '已保存到相册',\n            icon: 'success'\n          })\n          \n          return {\n            success: true,\n            fileName: fileName\n          }\n        } else {\n          throw new Error(`下载失败: ${downloadRes.statusCode}`)\n        }\n      } catch (error) {\n        console.error('下载或保存失败:', error)\n        // 如果是因为用户拒绝授权导致的失败\n        if (error.errMsg && error.errMsg.includes('auth deny')) {\n          uni.showModal({\n            title: '授权提示',\n            content: '需要授权保存到相册才能下载视频，是否去授权？',\n            success: (res) => {\n              if (res.confirm) {\n                uni.openSetting()\n              }\n            }\n          })\n          throw new Error('需要授权保存到相册')\n        }\n        throw error\n      }\n      // #endif\n      \n      // #ifdef H5\n      // H5端继续使用POST请求\n      const downloadUrl = `${this.baseUrl}/api/tools/media/download`\n      console.log('下载请求URL:', downloadUrl, '参数:', requestParams)\n      \n      // 调用下载接口\n      const response = await uni.request({\n        url: downloadUrl,\n        method: 'POST',\n        data: requestParams,\n        responseType: 'arraybuffer',\n        header: {\n          'Content-Type': 'application/json',\n          'Accept': 'video/mp4, */*'\n        }\n      })\n      \n      if (response.statusCode === 200) {\n        this.triggerDownload(response.data, fileName)\n        return {\n          success: true,\n          fileName: fileName\n        }\n      } else {\n        console.error('下载失败，状态码:', response.statusCode)\n        throw new Error(`下载失败: ${response.statusCode}`)\n      }\n      // #endif\n      \n    } catch (error) {\n      hideLoading()\n      console.error('视频下载失败:', error)\n      throw error\n    } finally {\n      hideLoading()\n    }\n  }\n\n  /**\n   * H5端触发文件下载\n   */\n  triggerDownload(data, filename) {\n    // #ifdef H5\n    // 确保文件名以.mp4结尾\n    if (!filename.toLowerCase().endsWith('.mp4')) {\n      const lastDotIndex = filename.lastIndexOf('.')\n      if (lastDotIndex > 0) {\n        filename = filename.substring(0, lastDotIndex)\n      }\n      filename += '.mp4'\n    }\n    \n    const blob = new Blob([data], { type: 'video/mp4' })\n    const url = window.URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = filename\n    a.style.display = 'none'\n    document.body.appendChild(a)\n    a.click()\n    document.body.removeChild(a)\n    window.URL.revokeObjectURL(url)\n    // #endif\n  }\n\n  /**\n   * 音乐下载器（搜索）\n   */\n  async downloadMusic(params) {\n    try {\n      showLoading('搜索中...')\n      const result = await this.callToolAPI('/api/tools/media/music-downloader', 'POST', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n  \n  /**\n   * 音乐文件下载\n   */\n  async downloadMusicFile(params) {\n    try {\n      showLoading('准备下载...')\n      const result = await this.callToolAPI('/api/tools/media/music-download-file', 'POST', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  // 获取音乐字节流（用于微信小程序直接下载）\n  async downloadMusicStream(params) {\n    try {\n      showLoading('准备下载...')\n      const result = await this.callToolAPI('/api/tools/media/download-music-stream', 'POST', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n  \n  /**\n   * 获取音乐预览URL\n   */\n  async getPreviewUrl(params) {\n    try {\n      showLoading('获取预览链接...')\n      const result = await this.callToolAPI('/api/tools/media/get-preview-url', 'POST', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 直接下载音乐文件到本地\n   */\n  async downloadMusicToLocal(filePath, fileName) {\n    try {\n      const downloadUrl = `${this.baseUrl}/api/tools/media/download-file?filePath=${encodeURIComponent(filePath)}&fileName=${encodeURIComponent(fileName)}`\n      \n      // #ifdef H5\n      // H5端直接通过链接下载\n      const link = document.createElement('a')\n      link.href = downloadUrl\n      link.download = fileName\n      link.style.display = 'none'\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n      return { success: true }\n      // #endif\n      \n      // #ifdef MP-WEIXIN\n      // 小程序端使用downloadFile\n      const downloadRes = await uni.downloadFile({\n        url: downloadUrl,\n        header: {\n          'Accept': 'audio/mpeg, */*'\n        }\n      })\n      \n      if (downloadRes.statusCode === 200) {\n        // 小程序中无法直接保存到相册，提示用户\n        uni.showModal({\n          title: '下载完成',\n          content: '文件已下载到临时目录，请注意及时保存',\n          showCancel: false\n        })\n        return { success: true, tempFilePath: downloadRes.tempFilePath }\n      } else {\n        throw new Error(`下载失败: ${downloadRes.statusCode}`)\n      }\n      // #endif\n      \n    } catch (error) {\n      console.error('下载失败:', error)\n      throw error\n    }\n  }\n\n  /**\n   * 文案提取器\n   */\n  async extractText(imageUrl) {\n    try {\n      showLoading('识别中...')\n      const result = await this.callToolAPI('/api/tools/media/text-extractor', 'POST', { imageUrl })\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 游戏语音合成\n   */\n  async synthesizeGameVoice(params) {\n    try {\n      showLoading('合成中...')\n      const result = await this.callToolAPI('/api/tools/media/game-voice-synthesizer', 'POST', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 音效大全\n   */\n  async getSoundEffects(category) {\n    try {\n      showLoading('加载中...')\n      const result = await this.callToolAPI('/api/tools/media/sound-effects-library', 'GET', { category })\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 魔法抹除水印\n   */\n  async magicWatermarkRemover(imageUrl) {\n    try {\n      showLoading('处理中...')\n      const result = await this.callToolAPI('/api/tools/media/magic-watermark-remover', 'POST', { imageUrl })\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 制作证件照\n   */\n  async makeIdPhoto(params) {\n    try {\n      console.log('开始制作证件照，参数:', params)\n      \n      // 使用专门的证件照API调用方法\n      const result = await this.callIdPhotoAPI(params)\n      \n      console.log('证件照制作完成:', result)\n      return result\n    } catch (error) {\n      console.error('证件照制作失败:', error)\n      throw error\n    }\n  }\n\n  /**\n   * 上传证件照原图\n   * @param {File} file - 图片文件\n   * @returns {Promise} 上传结果\n   */\n  async uploadIdPhoto(file) {\n    const formData = new FormData()\n    formData.append('file', file)\n    \n    const result = await this.request({\n      url: '/api/upload/idphoto',\n      method: 'POST',\n      data: formData,\n      header: {\n        'Content-Type': 'multipart/form-data'\n      },\n      timeout: 60000 // 60秒超时\n    })\n    \n    return result\n  }\n\n  /**\n   * 上传头像\n   * @param {File} file - 图片文件\n   * @param {string} userId - 用户ID（可选）\n   * @returns {Promise} 上传结果\n   */\n  async uploadAvatar(file, userId = null) {\n    const formData = new FormData()\n    formData.append('file', file)\n    if (userId) {\n      formData.append('userId', userId)\n    }\n    \n    const result = await this.request({\n      url: '/api/upload/avatar',\n      method: 'POST',\n      data: formData,\n      header: {\n        'Content-Type': 'multipart/form-data'\n      },\n      timeout: 60000\n    })\n    \n    return result\n  }\n\n  /**\n   * 上传壁纸\n   * @param {File} file - 图片文件\n   * @param {string} category - 分类（可选）\n   * @returns {Promise} 上传结果\n   */\n  async uploadWallpaper(file, category = null) {\n    const formData = new FormData()\n    formData.append('file', file)\n    if (category) {\n      formData.append('category', category)\n    }\n    \n    const result = await this.request({\n      url: '/api/upload/wallpaper',\n      method: 'POST',\n      data: formData,\n      header: {\n        'Content-Type': 'multipart/form-data'\n      },\n      timeout: 60000\n    })\n    \n    return result\n  }\n\n  /**\n   * 通用文件上传\n   * @param {File} file - 文件\n   * @param {string} type - 文件类型（可选）\n   * @returns {Promise} 上传结果\n   */\n  async uploadCommon(file, type = null) {\n    const formData = new FormData()\n    formData.append('file', file)\n    if (type) {\n      formData.append('type', type)\n    }\n    \n    const result = await this.request({\n      url: '/api/upload/common',\n      method: 'POST',\n      data: formData,\n      header: {\n        'Content-Type': 'multipart/form-data'\n      },\n      timeout: 60000\n    })\n    \n    return result\n  }\n\n  /**\n   * 获取上传凭证\n   * @param {string} type - 文件类型（可选）\n   * @returns {Promise} 上传凭证\n   */\n  async getUploadToken(type = null) {\n    const params = type ? { type } : {}\n    \n    const result = await this.request({\n      url: '/api/upload/token',\n      method: 'GET',\n      data: params\n    })\n    \n    return result\n  }\n\n  /**\n   * 删除文件\n   * @param {string} url - 文件URL\n   * @returns {Promise} 删除结果\n   */\n  async deleteFile(url) {\n    const result = await this.request({\n      url: '/api/upload/delete',\n      method: 'DELETE',\n      data: { url }\n    })\n    \n    return result\n  }\n\n  /**\n   * 专门用于证件照制作的API调用（支持超时设置）\n   * @param {Object} params - 请求参数\n   * @returns {Promise} 处理结果\n   */\n  async callIdPhotoAPI(params) {\n    return new Promise((resolve, reject) => {\n      uni.request({\n        url: this.baseUrl + '/api/tools/media/id-photo-maker',\n        method: 'POST',\n        data: params,\n        header: {\n          'Content-Type': 'application/json'\n        },\n        timeout: 180000, // 3分钟超时\n        success: (res) => {\n          if (res.statusCode === 200) {\n            resolve(res.data)\n          } else {\n            reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`))\n          }\n        },\n        fail: (err) => {\n          reject(err)\n        }\n      })\n    })\n  }\n\n  // ============ 实用工具 ============\n\n  /**\n   * 车辆价格查询\n   */\n  async queryVehiclePrice(params) {\n    try {\n      showLoading('查询中...')\n      const result = await this.callToolAPI('/api/tools/utility/vehicle-price-query', 'POST', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 全国油价查询\n   */\n  async queryGasPrice(params) {\n    try {\n      showLoading('查询中...')\n      const result = await this.callToolAPI('/api/tools/utility/gas-price-query', 'POST', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 全国天气查询\n   */\n  async queryWeather(params) {\n    try {\n      showLoading('查询中...')\n      const result = await this.callToolAPI('/api/tools/utility/weather-query', 'POST', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  // ============ 趣味工具 ============\n\n  /**\n   * 兽语加密解密\n   */\n  async beastLanguage(params) {\n    try {\n      showLoading('处理中...')\n      const result = await this.callToolAPI('/api/tools/fun/beast-language', 'POST', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 历史上的今天\n   */\n  async getHistoryToday(params = {}) {\n    try {\n      showLoading('查询历史事件...')\n      const result = await this.callToolAPI('/api/tools/fun/history-today', 'POST', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 随机头像\n   */\n  async getRandomAvatars(params = {}) {\n    try {\n      showLoading('生成头像中...')\n      \n      // 转换参数\n      const gender = params.gender === 'male' ? 'boy' : \n                    params.gender === 'female' ? 'girl' : 'boy'\n      \n      // 女生头像风格类型处理\n      let girlType = 1 // 默认酷girl\n      if (params.gender === 'female' && params.girlType) {\n        girlType = params.girlType\n      } else {\n        // 根据通用风格映射到女生风格\n        girlType = params.style === 'cartoon' ? 2 : \n                  params.style === 'realistic' ? 1 :\n                  params.style === 'pixel' ? 4 : 1\n      }\n      \n      // 手动构建查询参数字符串\n      const queryParams = `gender=${gender}&girlType=${girlType}&returnType=json`\n      \n      const result = await this.callToolAPI(`/api/tools/wallpaper/random-avatar?${queryParams}`, 'GET')\n      hideLoading()\n      \n      // 处理返回结果，生成多个头像\n      if (result.code === 200 && result.data && result.data.success) {\n        const avatars = []\n        const count = params.count || 3  // 改为3个头像\n        \n        // 生成多个不同的头像\n        for (let i = 0; i < count; i++) {\n          const singleResult = await this.callToolAPI(`/api/tools/wallpaper/random-avatar?${queryParams}`, 'GET')\n          if (singleResult.code === 200 && singleResult.data && singleResult.data.avatar) {\n            avatars.push(singleResult.data.avatar)\n          }\n          // 添加延迟避免过于频繁的请求\n          if (i < count - 1) {\n            await new Promise(resolve => setTimeout(resolve, 200))  // 增加延迟到200ms\n          }\n        }\n        \n        return {\n          success: true,\n          data: {\n            avatars: avatars,\n            gender: result.data.gender,\n            type: result.data.type\n          },\n          message: '生成成功'\n        }\n      } else {\n        throw new Error(result.message || '生成失败')\n      }\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 显卡天梯图\n   */\n  async getGpuLadder() {\n    try {\n      showLoading('加载中...')\n      const result = await this.callToolAPI('/api/tools/fun/gpu-ladder', 'GET')\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * CPU天梯图\n   */\n  async getCpuLadder() {\n    try {\n      showLoading('加载中...')\n      const result = await this.callToolAPI('/api/tools/fun/cpu-ladder', 'GET')\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  // ============ 程序员工具 ============\n\n  /**\n   * 生成网站快照\n   */\n  async generateWebsiteSnapshot(params) {\n    try {\n      showLoading('生成中...')\n\n      // 构建请求参数\n      const requestParams = {\n        url: params.url,\n        device: params.device || 'desktop',\n        format: params.format || 'png',\n        quality: params.quality || 90,\n        delay: params.delay || 2,\n        fullPage: params.fullPage || false,\n        removeAds: params.removeAds || false\n      }\n\n      console.log('发送网站快照请求:', requestParams)\n      const result = await this.callToolAPI('/api/tools/dev/website-snapshot', 'POST', requestParams)\n      console.log('网站快照返回结果:', result)\n\n      hideLoading()\n      return result\n    } catch (error) {\n      console.error('网站快照生成失败:', error)\n      hideLoading()\n      throw error\n    }\n  }\n\n  // ============ 图片壁纸工具 ============\n\n  /**\n   * Bing每日一图\n   */\n  async getBingDailyWallpaper() {\n    try {\n      showLoading('加载中...')\n      const result = await this.callToolAPI('/api/tools/wallpaper/bing-daily-wallpaper', 'GET')\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 获取随机头像\n   */\n  async getRandomAvatar(params) {\n    try {\n      showLoading('获取中...')\n      const result = await this.callToolAPI('/api/tools/wallpaper/random-avatar', 'GET', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 获取随机加载图\n   */\n  async getRandomLoadingImage(params) {\n    try {\n      showLoading('获取中...')\n      const result = await this.callToolAPI('/api/tools/wallpaper/random-loading-image', 'GET', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * Bing每日一图\n   */\n  async getBingDailyWallpaper() {\n    try {\n      showLoading('获取中...')\n      const result = await this.callToolAPI('/api/tools/wallpaper/bing-daily-wallpaper', 'GET')\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 随机PC壁纸\n   */\n  async getRandomPcWallpaper(params) {\n    try {\n      showLoading('获取中...')\n      const result = await this.callToolAPI('/api/tools/wallpaper/random-pc-wallpaper', 'GET', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  /**\n   * 动漫壁纸\n   */\n  async getAnimeWallpapers(params) {\n    try {\n      showLoading('获取中...')\n      const result = await this.callToolAPI('/api/tools/wallpaper/anime-wallpapers', 'GET', params)\n      hideLoading()\n      return result\n    } catch (error) {\n      hideLoading()\n      throw error\n    }\n  }\n\n  // ============ 通用API调用方法 ============\n\n  /**\n   * 调用工具API\n   */\n  async callToolAPI(url, method = 'GET', params = {}) {\n    try {\n      const requestUrl = this.baseUrl + url\n      console.log(`发起${method}请求:`, requestUrl, params)\n\n      // 根据不同的API设置不同的超时时间\n      let timeout = 10000 // 默认10秒\n      let maxRetries = 1 // 默认重试1次\n\n      if (url.includes('id-photo-maker')) {\n        timeout = 120000 // 证件照制作设置120秒超时\n        maxRetries = 0 // 证件照制作不重试，避免重复处理\n      } else if (url.includes('website-snapshot')) {\n        timeout = 120000 // 网站快照设置120秒超时\n        maxRetries = 0 // 网站快照不重试，避免重复处理\n      } else if (url.includes('video') || url.includes('image')) {\n        timeout = 60000 // 其他媒体处理类API设置60秒超时\n      }\n\n      // 重试机制\n      for (let attempt = 0; attempt <= maxRetries; attempt++) {\n        try {\n          const response = await uni.request({\n            url: requestUrl,\n            method: method,\n            data: method === 'GET' ? params : JSON.stringify(params),\n            header: {\n              'Content-Type': 'application/json'\n            },\n            timeout: timeout\n          })\n\n          console.log('API响应:', response)\n\n          // 检查响应状态\n          if (response.statusCode !== 200) {\n            throw new Error(`请求失败: ${response.statusCode}`)\n          }\n\n          // 检查响应数据\n          const result = response.data\n          if (!result) {\n            throw new Error('响应数据为空')\n          }\n\n          return result\n        } catch (error) {\n          console.log(`第${attempt + 1}次请求失败:`, error)\n          \n          // 如果是最后一次尝试，抛出错误\n          if (attempt === maxRetries) {\n            throw error\n          }\n          \n          // 等待1秒后重试\n          await new Promise(resolve => setTimeout(resolve, 1000))\n        }\n      }\n    } catch (error) {\n      console.error('API调用失败:', error)\n      throw error\n    }\n  }\n\n  // ============ 文件处理方法 ============\n\n  /**\n   * 选择并上传图片\n   */\n  async selectAndUploadImage() {\n    return new Promise((resolve, reject) => {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          const tempFilePath = res.tempFilePaths[0]\n          // 这里可以添加上传逻辑\n          resolve({\n            path: tempFilePath,\n            size: res.tempFiles[0].size\n          })\n        },\n        fail: (err) => {\n          reject(new Error('选择图片失败'))\n        }\n      })\n    })\n  }\n\n  /**\n   * 选择并上传音频\n   */\n  async selectAndUploadAudio() {\n    return new Promise((resolve, reject) => {\n      uni.chooseFile({\n        count: 1,\n        type: 'file',\n        extension: ['mp3', 'wav', 'aac'],\n        success: (res) => {\n          const tempFilePath = res.tempFilePaths[0]\n          resolve({\n            path: tempFilePath,\n            size: res.tempFiles[0].size\n          })\n        },\n        fail: (err) => {\n          reject(new Error('选择音频失败'))\n        }\n      })\n    })\n  }\n\n  /**\n   * 选择并上传视频\n   */\n  async selectAndUploadVideo() {\n    return new Promise((resolve, reject) => {\n      uni.chooseVideo({\n        sourceType: ['album', 'camera'],\n        maxDuration: 60,\n        camera: 'back',\n        success: (res) => {\n          resolve({\n            path: res.tempFilePath,\n            duration: res.duration,\n            size: res.size\n          })\n        },\n        fail: (err) => {\n          reject(new Error('选择视频失败'))\n        }\n      })\n    })\n  }\n\n  /**\n   * 下载文件\n   */\n  async downloadFile(url, filename) {\n    return new Promise((resolve, reject) => {\n      uni.downloadFile({\n        url: url,\n        success: (res) => {\n          if (res.statusCode === 200) {\n            uni.saveImageToPhotosAlbum({\n              filePath: res.tempFilePath,\n              success: () => {\n                resolve(res.tempFilePath)\n              },\n              fail: (err) => {\n                reject(new Error('保存失败'))\n              }\n            })\n          } else {\n            reject(new Error('下载失败'))\n          }\n        },\n        fail: (err) => {\n          reject(new Error('下载失败'))\n        }\n      })\n    })\n  }\n\n  /**\n   * 预览图片\n   */\n  previewImage(urls, current = 0) {\n    uni.previewImage({\n      urls: Array.isArray(urls) ? urls : [urls],\n      current: current\n    })\n  }\n\n  /**\n   * 判断是否为图片URL\n   */\n  isImageUrl(url) {\n    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']\n    return imageExtensions.some(ext => url.toLowerCase().includes(ext))\n  }\n\n  /**\n   * 复制URL到剪贴板\n   */\n  async copyUrl(url) {\n    return new Promise((resolve, reject) => {\n      uni.setClipboardData({\n        data: url,\n        success: () => {\n          showSuccess('链接已复制到剪贴板')\n          resolve()\n        },\n        fail: (err) => {\n          reject(new Error('复制失败'))\n        }\n      })\n    })\n  }\n\n  /**\n   * 分享内容\n   */\n  async shareContent(title, url, imageUrl) {\n    return new Promise((resolve, reject) => {\n      uni.share({\n        provider: 'weixin',\n        scene: 'WXSceneSession',\n        type: 0,\n        href: url,\n        title: title,\n        summary: title,\n        imageUrl: imageUrl,\n        success: () => {\n          resolve()\n        },\n        fail: (err) => {\n          reject(new Error('分享失败'))\n        }\n      })\n    })\n  }\n\n  /**\n   * 获取工具配置\n   */\n  getToolConfig(toolIdentifier) {\n    // 工具配置映射\n    const toolConfigs = {\n      'fun-image-generator': {\n        name: '趣味图片生成器',\n        icon: '🎨',\n        needsVip: false,\n        maxFileSize: 10 * 1024 * 1024, // 10MB\n        supportedFormats: ['jpg', 'png', 'gif']\n      },\n      'get-vip-membership': {\n        name: '获取永久会员',\n        icon: '👑',\n        needsVip: false,\n        description: '获取永久会员权益'\n      },\n      'video-watermark-remover': {\n        name: '视频去水印',\n        icon: '🎬',\n        needsVip: true,\n        maxFileSize: 100 * 1024 * 1024, // 100MB\n        supportedFormats: ['mp4', 'avi', 'mov']\n      },\n      // 可以继续添加其他工具的配置...\n    }\n\n    return toolConfigs[toolIdentifier] || {}\n  }\n}\n\n// 创建并导出单例实例\nexport const toolService = new ToolService();\n\n// 为了向后兼容，保持默认导出\nexport default toolService; "], "names": ["showLoading", "hideLoading", "authManager", "uni", "showSuccess"], "mappings": ";;;;AAKA,MAAM,WAAW;AAKV,MAAM,YAAY;AAAA,EACvB,cAAc;AACZ,SAAK,UAAU;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,YAAY,gBAAgB,SAAS,IAAI;AAC7C,QAAI;AACFA,kBAAAA,YAAY,QAAQ;AAGpB,YAAM,KAAK,oBAAqB;AAEhC,YAAM,SAAS,MAAM,KAAK,YAAY,cAAc,cAAc,IAAI,QAAQ,MAAM;AACpFC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,sBAAsB;AAC1B,QAAI;AAEF,UAAIC,kBAAAA,YAAY,cAAc;AAC5B,eAAO;AAAA,MACR;AAGDC,oBAAAA,iDAAY,WAAW;AACvB,YAAM,eAAe,MAAMD,kBAAW,YAAC,YAAa;AAEpD,UAAI,CAAC,cAAc;AACjBC,sBAAAA,MAAA,MAAA,QAAA,8BAAa,uBAAuB;AAAA,MACrC;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,8BAAA,SAAS,KAAK;AAE5B,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,iBAAiB,QAAQ;AAC7B,QAAI;AACFH,kBAAAA,YAAY,QAAQ;AAGpB,YAAM,KAAK,oBAAqB;AAEhC,YAAM,SAAS,MAAM,KAAK,YAAY,sCAAsC,QAAQ,MAAM;AAC1FC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,iBAAiB,QAAQ;AAC7B,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,qCAAqC,QAAQ,MAAM;AACzFC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,qBAAqB,UAAU;AACnC,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,4CAA4C,QAAQ,EAAE,UAAU;AACtGC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,qBAAqB,WAAW;AACpC,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,4CAA4C,QAAQ,EAAE,WAAW;AACvGC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,cAAc,QAAQ;AAC1B,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AAEpB,YAAM,gBAAgB;AAAA,QACpB,UAAU,OAAO,YAAY,OAAO;AAAA;AAAA,QACpC,SAAS,OAAO,WAAW;AAAA,QAC3B,QAAQ,OAAO,UAAU;AAAA,MAC1B;AAEDG,oBAAAA,MAAY,MAAA,OAAA,+BAAA,aAAa,aAAa;AACtC,YAAM,SAAS,MAAM,KAAK,YAAY,qCAAqC,QAAQ,aAAa;AAChGA,oBAAAA,kDAAY,aAAa,MAAM;AAE/BF,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdE,oBAAAA,MAAc,MAAA,SAAA,+BAAA,WAAW,KAAK;AAC9BF,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,kBAAkB,QAAQ;AAC9B,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AAGpB,UAAI,WAAW,OAAO,YAAY,SAAS,KAAK,IAAK,CAAA;AAGrD,UAAI,CAAC,SAAS,YAAa,EAAC,SAAS,MAAM,GAAG;AAE5C,cAAM,eAAe,SAAS,YAAY,GAAG;AAC7C,YAAI,eAAe,GAAG;AACpB,qBAAW,SAAS,UAAU,GAAG,YAAY;AAAA,QAC9C;AACD,oBAAY;AAAA,MACb;AAGD,iBAAW,SAAS,QAAQ,iBAAiB,GAAG;AAEhD,YAAM,gBAAgB;AAAA,QACpB,UAAU,OAAO;AAAA,QACjB;AAAA,QACA,SAAS,OAAO;AAAA,MACjB;AAGD,UAAI,CAAC,KAAK,SAAS;AACjBG,sBAAAA,oDAAc,YAAY;AAC1B,cAAM,IAAI,MAAM,UAAU;AAAA,MAC3B;AAGD,UAAI;AAEF,cAAM,cAAc,OAAO,KAAK,aAAa,EAC1C,OAAO,SAAO,cAAc,GAAG,MAAM,UAAa,cAAc,GAAG,MAAM,IAAI,EAC7E,IAAI,SAAO,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,cAAc,GAAG,CAAC,CAAC,EAAE,EACjF,KAAK,GAAG;AAEX,cAAM,cAAc,GAAG,KAAK,OAAO,6BAA6B,WAAW;AAC3EA,sBAAAA,MAAA,MAAA,OAAA,+BAAY,YAAY,WAAW;AAGnC,cAAM,cAAc,MAAMA,cAAG,MAAC,aAAa;AAAA,UACzC,KAAK;AAAA,UACL,QAAQ;AAAA,YACN,UAAU;AAAA,UACX;AAAA,QACX,CAAS;AAEDA,sBAAAA,MAAA,MAAA,OAAA,+BAAY,SAAS,WAAW;AAEhC,YAAI,YAAY,eAAe,KAAK;AAElC,gBAAMA,cAAAA,MAAI,uBAAuB;AAAA,YAC/B,UAAU,YAAY;AAAA,UAClC,CAAW;AAEDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAED,iBAAO;AAAA,YACL,SAAS;AAAA,YACT;AAAA,UACD;AAAA,QACX,OAAe;AACL,gBAAM,IAAI,MAAM,SAAS,YAAY,UAAU,EAAE;AAAA,QAClD;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,+BAAc,YAAY,KAAK;AAE/B,YAAI,MAAM,UAAU,MAAM,OAAO,SAAS,WAAW,GAAG;AACtDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,SAAS,CAAC,QAAQ;AAChB,kBAAI,IAAI,SAAS;AACfA,8BAAAA,MAAI,YAAa;AAAA,cAClB;AAAA,YACF;AAAA,UACb,CAAW;AACD,gBAAM,IAAI,MAAM,WAAW;AAAA,QAC5B;AACD,cAAM;AAAA,MACP;AAAA,IAgCF,SAAQ,OAAO;AACdF,8BAAa;AACbE,oBAAAA,MAAc,MAAA,SAAA,+BAAA,WAAW,KAAK;AAC9B,YAAM;AAAA,IACZ,UAAc;AACRF,8BAAa;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,gBAAgB,MAAM,UAAU;AAAA,EAsB/B;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,cAAc,QAAQ;AAC1B,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,qCAAqC,QAAQ,MAAM;AACzFC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,kBAAkB,QAAQ;AAC9B,QAAI;AACFD,kBAAAA,YAAY,SAAS;AACrB,YAAM,SAAS,MAAM,KAAK,YAAY,wCAAwC,QAAQ,MAAM;AAC5FC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,oBAAoB,QAAQ;AAChC,QAAI;AACFD,kBAAAA,YAAY,SAAS;AACrB,YAAM,SAAS,MAAM,KAAK,YAAY,0CAA0C,QAAQ,MAAM;AAC9FC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,cAAc,QAAQ;AAC1B,QAAI;AACFD,kBAAAA,YAAY,WAAW;AACvB,YAAM,SAAS,MAAM,KAAK,YAAY,oCAAoC,QAAQ,MAAM;AACxFC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,qBAAqB,UAAU,UAAU;AAC7C,QAAI;AACF,YAAM,cAAc,GAAG,KAAK,OAAO,2CAA2C,mBAAmB,QAAQ,CAAC,aAAa,mBAAmB,QAAQ,CAAC;AAgBnJ,YAAM,cAAc,MAAME,cAAG,MAAC,aAAa;AAAA,QACzC,KAAK;AAAA,QACL,QAAQ;AAAA,UACN,UAAU;AAAA,QACX;AAAA,MACT,CAAO;AAED,UAAI,YAAY,eAAe,KAAK;AAElCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,QACtB,CAAS;AACD,eAAO,EAAE,SAAS,MAAM,cAAc,YAAY,aAAc;AAAA,MACxE,OAAa;AACL,cAAM,IAAI,MAAM,SAAS,YAAY,UAAU,EAAE;AAAA,MAClD;AAAA,IAGF,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,+BAAA,SAAS,KAAK;AAC5B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,YAAY,UAAU;AAC1B,QAAI;AACFH,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,mCAAmC,QAAQ,EAAE,UAAU;AAC7FC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,oBAAoB,QAAQ;AAChC,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,2CAA2C,QAAQ,MAAM;AAC/FC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,gBAAgB,UAAU;AAC9B,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,0CAA0C,OAAO,EAAE,UAAU;AACnGC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,sBAAsB,UAAU;AACpC,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,4CAA4C,QAAQ,EAAE,UAAU;AACtGC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,YAAY,QAAQ;AACxB,QAAI;AACFE,oBAAAA,MAAY,MAAA,OAAA,+BAAA,eAAe,MAAM;AAGjC,YAAM,SAAS,MAAM,KAAK,eAAe,MAAM;AAE/CA,oBAAAA,kDAAY,YAAY,MAAM;AAC9B,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,+BAAA,YAAY,KAAK;AAC/B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,cAAc,MAAM;AACxB,UAAM,WAAW,IAAI,SAAU;AAC/B,aAAS,OAAO,QAAQ,IAAI;AAE5B,UAAM,SAAS,MAAM,KAAK,QAAQ;AAAA,MAChC,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,gBAAgB;AAAA,MACjB;AAAA,MACD,SAAS;AAAA;AAAA,IACf,CAAK;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,aAAa,MAAM,SAAS,MAAM;AACtC,UAAM,WAAW,IAAI,SAAU;AAC/B,aAAS,OAAO,QAAQ,IAAI;AAC5B,QAAI,QAAQ;AACV,eAAS,OAAO,UAAU,MAAM;AAAA,IACjC;AAED,UAAM,SAAS,MAAM,KAAK,QAAQ;AAAA,MAChC,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,gBAAgB;AAAA,MACjB;AAAA,MACD,SAAS;AAAA,IACf,CAAK;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,gBAAgB,MAAM,WAAW,MAAM;AAC3C,UAAM,WAAW,IAAI,SAAU;AAC/B,aAAS,OAAO,QAAQ,IAAI;AAC5B,QAAI,UAAU;AACZ,eAAS,OAAO,YAAY,QAAQ;AAAA,IACrC;AAED,UAAM,SAAS,MAAM,KAAK,QAAQ;AAAA,MAChC,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,gBAAgB;AAAA,MACjB;AAAA,MACD,SAAS;AAAA,IACf,CAAK;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,aAAa,MAAM,OAAO,MAAM;AACpC,UAAM,WAAW,IAAI,SAAU;AAC/B,aAAS,OAAO,QAAQ,IAAI;AAC5B,QAAI,MAAM;AACR,eAAS,OAAO,QAAQ,IAAI;AAAA,IAC7B;AAED,UAAM,SAAS,MAAM,KAAK,QAAQ;AAAA,MAChC,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,gBAAgB;AAAA,MACjB;AAAA,MACD,SAAS;AAAA,IACf,CAAK;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,eAAe,OAAO,MAAM;AAChC,UAAM,SAAS,OAAO,EAAE,KAAM,IAAG,CAAE;AAEnC,UAAM,SAAS,MAAM,KAAK,QAAQ;AAAA,MAChC,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,WAAW,KAAK;AACpB,UAAM,SAAS,MAAM,KAAK,QAAQ;AAAA,MAChC,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM,EAAE,IAAK;AAAA,IACnB,CAAK;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,eAAe,QAAQ;AAC3B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK,KAAK,UAAU;AAAA,QACpB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,gBAAgB;AAAA,QACjB;AAAA,QACD,SAAS;AAAA;AAAA,QACT,SAAS,CAAC,QAAQ;;AAChB,cAAI,IAAI,eAAe,KAAK;AAC1B,oBAAQ,IAAI,IAAI;AAAA,UAC5B,OAAiB;AACL,mBAAO,IAAI,MAAM,QAAQ,IAAI,UAAU,OAAK,SAAI,SAAJ,mBAAU,YAAW,MAAM,EAAE,CAAC;AAAA,UAC3E;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACb,iBAAO,GAAG;AAAA,QACX;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,kBAAkB,QAAQ;AAC9B,QAAI;AACFH,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,0CAA0C,QAAQ,MAAM;AAC9FC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,cAAc,QAAQ;AAC1B,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,sCAAsC,QAAQ,MAAM;AAC1FC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,aAAa,QAAQ;AACzB,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,oCAAoC,QAAQ,MAAM;AACxFC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,cAAc,QAAQ;AAC1B,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,iCAAiC,QAAQ,MAAM;AACrFC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,gBAAgB,SAAS,IAAI;AACjC,QAAI;AACFD,kBAAAA,YAAY,WAAW;AACvB,YAAM,SAAS,MAAM,KAAK,YAAY,gCAAgC,QAAQ,MAAM;AACpFC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,iBAAiB,SAAS,IAAI;AAClC,QAAI;AACFD,kBAAAA,YAAY,UAAU;AAGtB,YAAM,SAAS,OAAO,WAAW,SAAS,QAC5B,OAAO,WAAW,WAAW,SAAS;AAGpD,UAAI,WAAW;AACf,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,mBAAW,OAAO;AAAA,MAC1B,OAAa;AAEL,mBAAW,OAAO,UAAU,YAAY,IAC9B,OAAO,UAAU,cAAc,IAC/B,OAAO,UAAU,UAAU,IAAI;AAAA,MAC1C;AAGD,YAAM,cAAc,UAAU,MAAM,aAAa,QAAQ;AAEzD,YAAM,SAAS,MAAM,KAAK,YAAY,sCAAsC,WAAW,IAAI,KAAK;AAChGC,8BAAa;AAGb,UAAI,OAAO,SAAS,OAAO,OAAO,QAAQ,OAAO,KAAK,SAAS;AAC7D,cAAM,UAAU,CAAE;AAClB,cAAM,QAAQ,OAAO,SAAS;AAG9B,iBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,gBAAM,eAAe,MAAM,KAAK,YAAY,sCAAsC,WAAW,IAAI,KAAK;AACtG,cAAI,aAAa,SAAS,OAAO,aAAa,QAAQ,aAAa,KAAK,QAAQ;AAC9E,oBAAQ,KAAK,aAAa,KAAK,MAAM;AAAA,UACtC;AAED,cAAI,IAAI,QAAQ,GAAG;AACjB,kBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAAA,UACtD;AAAA,QACF;AAED,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM;AAAA,YACJ;AAAA,YACA,QAAQ,OAAO,KAAK;AAAA,YACpB,MAAM,OAAO,KAAK;AAAA,UACnB;AAAA,UACD,SAAS;AAAA,QACV;AAAA,MACT,OAAa;AACL,cAAM,IAAI,MAAM,OAAO,WAAW,MAAM;AAAA,MACzC;AAAA,IACF,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,eAAe;AACnB,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,6BAA6B,KAAK;AACxEC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,eAAe;AACnB,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,6BAA6B,KAAK;AACxEC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,wBAAwB,QAAQ;AACpC,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AAGpB,YAAM,gBAAgB;AAAA,QACpB,KAAK,OAAO;AAAA,QACZ,QAAQ,OAAO,UAAU;AAAA,QACzB,QAAQ,OAAO,UAAU;AAAA,QACzB,SAAS,OAAO,WAAW;AAAA,QAC3B,OAAO,OAAO,SAAS;AAAA,QACvB,UAAU,OAAO,YAAY;AAAA,QAC7B,WAAW,OAAO,aAAa;AAAA,MAChC;AAEDG,oBAAAA,MAAY,MAAA,OAAA,+BAAA,aAAa,aAAa;AACtC,YAAM,SAAS,MAAM,KAAK,YAAY,mCAAmC,QAAQ,aAAa;AAC9FA,oBAAAA,kDAAY,aAAa,MAAM;AAE/BF,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdE,oBAAAA,oDAAc,aAAa,KAAK;AAChCF,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,wBAAwB;AAC5B,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,6CAA6C,KAAK;AACxFC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,gBAAgB,QAAQ;AAC5B,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,sCAAsC,OAAO,MAAM;AACzFC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,sBAAsB,QAAQ;AAClC,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,6CAA6C,OAAO,MAAM;AAChGC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,wBAAwB;AAC5B,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,6CAA6C,KAAK;AACxFC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,qBAAqB,QAAQ;AACjC,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,4CAA4C,OAAO,MAAM;AAC/FC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,mBAAmB,QAAQ;AAC/B,QAAI;AACFD,kBAAAA,YAAY,QAAQ;AACpB,YAAM,SAAS,MAAM,KAAK,YAAY,yCAAyC,OAAO,MAAM;AAC5FC,8BAAa;AACb,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,8BAAa;AACb,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,YAAY,KAAK,SAAS,OAAO,SAAS,CAAA,GAAI;AAClD,QAAI;AACF,YAAM,aAAa,KAAK,UAAU;AAClCE,0BAAA,MAAA,OAAA,+BAAY,KAAK,MAAM,OAAO,YAAY,MAAM;AAGhD,UAAI,UAAU;AACd,UAAI,aAAa;AAEjB,UAAI,IAAI,SAAS,gBAAgB,GAAG;AAClC,kBAAU;AACV,qBAAa;AAAA,MACd,WAAU,IAAI,SAAS,kBAAkB,GAAG;AAC3C,kBAAU;AACV,qBAAa;AAAA,MACrB,WAAiB,IAAI,SAAS,OAAO,KAAK,IAAI,SAAS,OAAO,GAAG;AACzD,kBAAU;AAAA,MACX;AAGD,eAAS,UAAU,GAAG,WAAW,YAAY,WAAW;AACtD,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL;AAAA,YACA,MAAM,WAAW,QAAQ,SAAS,KAAK,UAAU,MAAM;AAAA,YACvD,QAAQ;AAAA,cACN,gBAAgB;AAAA,YACjB;AAAA,YACD;AAAA,UACZ,CAAW;AAEDA,wBAAAA,MAAA,MAAA,OAAA,+BAAY,UAAU,QAAQ;AAG9B,cAAI,SAAS,eAAe,KAAK;AAC/B,kBAAM,IAAI,MAAM,SAAS,SAAS,UAAU,EAAE;AAAA,UAC/C;AAGD,gBAAM,SAAS,SAAS;AACxB,cAAI,CAAC,QAAQ;AACX,kBAAM,IAAI,MAAM,QAAQ;AAAA,UACzB;AAED,iBAAO;AAAA,QACR,SAAQ,OAAO;AACdA,8BAAY,MAAA,OAAA,gCAAA,IAAI,UAAU,CAAC,UAAU,KAAK;AAG1C,cAAI,YAAY,YAAY;AAC1B,kBAAM;AAAA,UACP;AAGD,gBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;AAAA,QACvD;AAAA,MACF;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,gCAAA,YAAY,KAAK;AAC/B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,uBAAuB;AAC3B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,gBAAM,eAAe,IAAI,cAAc,CAAC;AAExC,kBAAQ;AAAA,YACN,MAAM;AAAA,YACN,MAAM,IAAI,UAAU,CAAC,EAAE;AAAA,UACnC,CAAW;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACb,iBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,QAC3B;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,uBAAuB;AAC3B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,WAAW;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW,CAAC,OAAO,OAAO,KAAK;AAAA,QAC/B,SAAS,CAAC,QAAQ;AAChB,gBAAM,eAAe,IAAI,cAAc,CAAC;AACxC,kBAAQ;AAAA,YACN,MAAM;AAAA,YACN,MAAM,IAAI,UAAU,CAAC,EAAE;AAAA,UACnC,CAAW;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACb,iBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,QAC3B;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,uBAAuB;AAC3B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,YAAY;AAAA,QACd,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,SAAS,CAAC,QAAQ;AAChB,kBAAQ;AAAA,YACN,MAAM,IAAI;AAAA,YACV,UAAU,IAAI;AAAA,YACd,MAAM,IAAI;AAAA,UACtB,CAAW;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACb,iBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,QAC3B;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,aAAa,KAAK,UAAU;AAChC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,aAAa;AAAA,QACf;AAAA,QACA,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,eAAe,KAAK;AAC1BA,0BAAAA,MAAI,uBAAuB;AAAA,cACzB,UAAU,IAAI;AAAA,cACd,SAAS,MAAM;AACb,wBAAQ,IAAI,YAAY;AAAA,cACzB;AAAA,cACD,MAAM,CAAC,QAAQ;AACb,uBAAO,IAAI,MAAM,MAAM,CAAC;AAAA,cACzB;AAAA,YACf,CAAa;AAAA,UACb,OAAiB;AACL,mBAAO,IAAI,MAAM,MAAM,CAAC;AAAA,UACzB;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACb,iBAAO,IAAI,MAAM,MAAM,CAAC;AAAA,QACzB;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,aAAa,MAAM,UAAU,GAAG;AAC9BA,kBAAAA,MAAI,aAAa;AAAA,MACf,MAAM,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAAA,MACxC;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,WAAW,KAAK;AACd,UAAM,kBAAkB,CAAC,QAAQ,SAAS,QAAQ,QAAQ,QAAQ,OAAO;AACzE,WAAO,gBAAgB,KAAK,SAAO,IAAI,cAAc,SAAS,GAAG,CAAC;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,QAAQ,KAAK;AACjB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,MAAM;AACbC,sBAAAA,YAAY,WAAW;AACvB,kBAAS;AAAA,QACV;AAAA,QACD,MAAM,CAAC,QAAQ;AACb,iBAAO,IAAI,MAAM,MAAM,CAAC;AAAA,QACzB;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,aAAa,OAAO,KAAK,UAAU;AACvC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCD,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN;AAAA,QACA,SAAS;AAAA,QACT;AAAA,QACA,SAAS,MAAM;AACb,kBAAS;AAAA,QACV;AAAA,QACD,MAAM,CAAC,QAAQ;AACb,iBAAO,IAAI,MAAM,MAAM,CAAC;AAAA,QACzB;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc,gBAAgB;AAE5B,UAAM,cAAc;AAAA,MAClB,uBAAuB;AAAA,QACrB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa,KAAK,OAAO;AAAA;AAAA,QACzB,kBAAkB,CAAC,OAAO,OAAO,KAAK;AAAA,MACvC;AAAA,MACD,sBAAsB;AAAA,QACpB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,MACd;AAAA,MACD,2BAA2B;AAAA,QACzB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa,MAAM,OAAO;AAAA;AAAA,QAC1B,kBAAkB,CAAC,OAAO,OAAO,KAAK;AAAA,MACvC;AAAA;AAAA,IAEF;AAED,WAAO,YAAY,cAAc,KAAK,CAAE;AAAA,EACzC;AACH;AAGY,MAAC,cAAc,IAAI,YAAW;;;"}