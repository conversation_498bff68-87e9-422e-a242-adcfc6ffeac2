"use strict";
const toolsData = [
  // 好玩推荐 (3个)
  {
    id: 1,
    name: "趣味图片生成器",
    icon: "🎨",
    category: "好玩推荐",
    description: "生成有趣的图片",
    color: "bg-pink-100",
    isNew: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "fun-image-generator"
  },
  {
    id: 2,
    name: "获取永久会员",
    icon: "👑",
    category: "好玩推荐",
    description: "会员权益获取",
    color: "bg-yellow-100",
    isInDevelopment: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "get-vip-membership"
  },
  {
    id: 3,
    name: "表白代码制作",
    icon: "💝",
    category: "好玩推荐",
    description: "制作浪漫表白代码",
    color: "bg-red-100",
    isDisabled: true,
    isUnlocked: false,
    needsBackend: false
  },
  // 媒体工具 (25个)
  {
    id: 4,
    name: "视频解析去水印",
    icon: "🎬",
    category: "媒体工具",
    description: "去除视频水印",
    color: "bg-blue-100",
    isNew: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "video-watermark-remover"
  },
  {
    id: 5,
    name: "图集解析去水印",
    icon: "🖼️",
    category: "媒体工具",
    description: "批量去图片水印",
    color: "bg-blue-100",
    isInDevelopment: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "image-watermark-remover"
  },
  {
    id: 6,
    name: "视频H下载",
    icon: "📥",
    category: "媒体工具",
    description: "高清视频下载",
    color: "bg-green-100",
    isNew: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "video-downloader"
  },
  { id: 7, name: "音频剪辑", icon: "🎵", category: "媒体工具", description: "在线音频编辑", color: "bg-green-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 8, name: "视频转音频", icon: "🔄", category: "媒体工具", description: "提取视频音轨", color: "bg-purple-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  {
    id: 9,
    name: "音乐下载器",
    icon: "🎼",
    category: "媒体工具",
    description: "音乐下载工具",
    color: "bg-pink-100",
    isNew: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "music-downloader"
  },
  {
    id: 10,
    name: "文案提取器",
    icon: "📝",
    category: "媒体工具",
    description: "提取图片文案",
    color: "bg-gray-100",
    isInDevelopment: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "text-extractor"
  },
  { id: 11, name: "九宫格切图", icon: "⚏", category: "媒体工具", description: "图片九宫格分割", color: "bg-orange-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 12, name: "图片压缩", icon: "📷", category: "媒体工具", description: "压缩图片大小", color: "bg-teal-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 13, name: "修改MD5", icon: "🔧", category: "媒体工具", description: "修改文件MD5值", color: "bg-slate-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 14, name: "截图加壳", icon: "📱", category: "媒体工具", description: "手机截图美化", color: "bg-indigo-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  {
    id: 15,
    name: "游戏语音合成",
    icon: "🎮",
    category: "媒体工具",
    description: "游戏角色语音",
    color: "bg-purple-100",
    isNew: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "game-voice-synthesizer"
  },
  {
    id: 16,
    name: "音效大全",
    icon: "🔊",
    category: "媒体工具",
    description: "各种音效资源",
    color: "bg-blue-100",
    isInDevelopment: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "sound-effects-library"
  },
  { id: 17, name: "图片拼接", icon: "🖼️", category: "媒体工具", description: "多图拼接合成", color: "bg-green-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 18, name: "黑白图转换", icon: "⚫", category: "媒体工具", description: "彩色转黑白", color: "bg-gray-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 19, name: "图片加水印", icon: "💧", category: "媒体工具", description: "添加图片水印", color: "bg-cyan-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 20, name: "图片打码", icon: "🔒", category: "媒体工具", description: "隐私信息打码", color: "bg-red-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  {
    id: 21,
    name: "魔法抹除水印",
    icon: "✨",
    category: "媒体工具",
    description: "AI智能去水印",
    color: "bg-purple-100",
    isNew: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "magic-watermark-remover"
  },
  {
    id: 22,
    name: "证件照制作",
    icon: "📸",
    category: "媒体工具",
    description: "制作标准证件照",
    color: "bg-indigo-100",
    isInDevelopment: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "id-photo-maker"
  },
  { id: 23, name: "视频压缩", icon: "🗜️", category: "媒体工具", description: "压缩视频文件", color: "bg-teal-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 24, name: "图片镜像翻转", icon: "🪞", category: "媒体工具", description: "图片镜像处理", color: "bg-blue-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 25, name: "图片像素化", icon: "🔲", category: "媒体工具", description: "像素风格转换", color: "bg-lime-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 26, name: "隐藏图制作", icon: "🫥", category: "媒体工具", description: "制作隐藏图片", color: "bg-gray-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  // 颜色工具 (8个)
  { id: 28, name: "色卡配色", icon: "🎨", category: "颜色工具", description: "专业配色方案", color: "bg-rose-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 29, name: "RGB与HEX", icon: "🌈", category: "颜色工具", description: "颜色格式转换", color: "bg-violet-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 30, name: "图片取色", icon: "🎯", category: "颜色工具", description: "从图片提取颜色", color: "bg-cyan-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 31, name: "渐变色卡", icon: "🌅", category: "颜色工具", description: "渐变色彩搭配", color: "bg-amber-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 32, name: "渐变代码生成", icon: "📋", category: "颜色工具", description: "CSS渐变代码", color: "bg-purple-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 33, name: "对比度检测", icon: "👁️", category: "颜色工具", description: "检查颜色对比度", color: "bg-lime-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 35, name: "色调生成器", icon: "🎪", category: "颜色工具", description: "色调变化生成", color: "bg-indigo-100", isNew: true, isUnlocked: false, needsBackend: false },
  // 实用工具 (9个)
  {
    id: 36,
    name: "车辆价格查询",
    icon: "🚗",
    category: "实用工具",
    description: "汽车价格查询",
    color: "bg-blue-100",
    isNew: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "vehicle-price-query"
  },
  {
    id: 37,
    name: "全国油价",
    icon: "⛽",
    category: "实用工具",
    description: "实时油价信息",
    color: "bg-yellow-100",
    isInDevelopment: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "gas-price-query"
  },
  { id: 38, name: "常用号码", icon: "📞", category: "实用工具", description: "常用电话号码", color: "bg-green-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 39, name: "手机清灰", icon: "🔊", category: "实用工具", description: "声波清理灰尘", color: "bg-gray-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 40, name: "手持弹幕", icon: "💬", category: "实用工具", description: "手持弹幕显示", color: "bg-pink-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 41, name: "BMI计算器", icon: "⚖️", category: "实用工具", description: "身体质量指数", color: "bg-emerald-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  {
    id: 42,
    name: "全国天气查询",
    icon: "🌤️",
    category: "实用工具",
    description: "实时天气信息",
    color: "bg-sky-100",
    isNew: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "weather-query"
  },
  { id: 43, name: "生成随机数", icon: "🎲", category: "实用工具", description: "随机数生成器", color: "bg-purple-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 44, name: "安全期计算", icon: "📅", category: "实用工具", description: "生理周期计算", color: "bg-pink-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  // 趣味工具 (15个)
  {
    id: 45,
    name: "兽语加密解密",
    icon: "🐾",
    category: "趣味工具",
    description: "兽语转换工具",
    color: "bg-orange-100",
    isNew: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "beast-language"
  },
  { id: 46, name: "偏心大转盘", icon: "🎡", category: "趣味工具", description: "随机选择转盘", color: "bg-rainbow", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 47, name: "起始时间计算", icon: "⏰", category: "趣味工具", description: "计算时间差", color: "bg-blue-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  {
    id: 48,
    name: "历史上的今天",
    icon: "📅",
    category: "趣味工具",
    description: "今日历史事件",
    color: "bg-purple-100",
    isNew: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "history-today"
  },
  { id: 49, name: "吃掉GIF头像", icon: "😋", category: "趣味工具", description: "趣味头像生成", color: "bg-yellow-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 50, name: "IP签名档", icon: "📝", category: "趣味工具", description: "IP地址签名", color: "bg-gray-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 51, name: "全屏时钟", icon: "⏰", category: "趣味工具", description: "大屏数字时钟", color: "bg-indigo-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 52, name: "模拟来电", icon: "📞", category: "趣味工具", description: "假装来电话", color: "bg-green-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 53, name: "手机检测", icon: "📱", category: "趣味工具", description: "手机性能检测", color: "bg-blue-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 54, name: "一生时间", icon: "🕐", category: "趣味工具", description: "人生时间计算", color: "bg-rose-100", isNew: true, isUnlocked: false, needsBackend: false },
  {
    id: 55,
    name: "显卡天梯图",
    icon: "🎮",
    category: "趣味工具",
    description: "显卡性能排行",
    color: "bg-purple-100",
    isInDevelopment: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "gpu-ladder"
  },
  {
    id: 56,
    name: "CPU天梯图",
    icon: "💻",
    category: "趣味工具",
    description: "CPU性能排行",
    color: "bg-cyan-100",
    isDisabled: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "cpu-ladder"
  },
  { id: 57, name: "功德木鱼", icon: "🪕", category: "趣味工具", description: "电子木鱼敲击", color: "bg-amber-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 58, name: "便携风扇", icon: "🌀", category: "趣味工具", description: "手机变身小风扇", color: "bg-cyan-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 59, name: "摇骰子", icon: "🎲", category: "趣味工具", description: "虚拟骰子游戏", color: "bg-pink-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  // 程序员工具 (20个)
  { id: 60, name: "PX与EM", icon: "📐", category: "程序员工具", description: "单位转换工具", color: "bg-blue-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 61, name: "时间戳工具", icon: "⏱️", category: "程序员工具", description: "时间戳转换", color: "bg-slate-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 62, name: "MD5加密", icon: "🔐", category: "程序员工具", description: "MD5哈希加密", color: "bg-gray-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 63, name: "ASCII转换", icon: "🔤", category: "程序员工具", description: "ASCII码转换", color: "bg-green-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 64, name: "动画代码生成", icon: "🎬", category: "程序员工具", description: "CSS动画生成", color: "bg-purple-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 65, name: "Grid布局生成", icon: "📏", category: "程序员工具", description: "CSS Grid生成", color: "bg-indigo-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 66, name: "Flex布局生成", icon: "📋", category: "程序员工具", description: "CSS Flex生成", color: "bg-teal-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 67, name: "磨砂玻璃生成", icon: "🪟", category: "程序员工具", description: "毛玻璃效果CSS", color: "bg-cyan-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 68, name: "CSS文字效果", icon: "✨", category: "程序员工具", description: "CSS特效生成", color: "bg-violet-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  {
    id: 69,
    name: "生成网站快照",
    icon: "📸",
    category: "程序员工具",
    description: "网站截图工具",
    color: "bg-orange-100",
    isNew: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "website-snapshot"
  },
  { id: 70, name: "端口检测", icon: "🔌", category: "程序员工具", description: "网络端口检测", color: "bg-red-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 71, name: "Unicode", icon: "🌐", category: "程序员工具", description: "Unicode编码", color: "bg-lime-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 72, name: "HTTP状态码", icon: "🌐", category: "程序员工具", description: "HTTP状态码查询", color: "bg-green-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 73, name: "HTTP请求头", icon: "📡", category: "程序员工具", description: "HTTP请求头查看", color: "bg-blue-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 74, name: "keyCode按键码", icon: "⌨️", category: "程序员工具", description: "键盘按键码", color: "bg-gray-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 75, name: "Linux常用命令", icon: "🐧", category: "程序员工具", description: "Linux命令查询", color: "bg-slate-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 76, name: "密码生成器", icon: "🔑", category: "程序员工具", description: "生成安全密码", color: "bg-red-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 77, name: "常用端口大全", icon: "", category: "程序员工具", description: "常用端口查询", color: "bg-yellow-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 78, name: "UA标识大全", icon: "🏷️", category: "程序员工具", description: "用户代理标识", color: "bg-pink-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 79, name: "UUID生成器", icon: "🆔", category: "程序员工具", description: "生成唯一标识符", color: "bg-blue-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  // 文字工具 (30个)
  { id: 80, name: "伤感文案库", icon: "😢", category: "文字工具", description: "伤感文案集合", color: "bg-blue-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 81, name: "疯狂星期四文案", icon: "🍗", category: "文字工具", description: "KFC文案生成器", color: "bg-orange-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 82, name: "答案之书", icon: "📚", category: "文字工具", description: "随机答案生成", color: "bg-purple-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 83, name: "名言警句", icon: "💭", category: "文字工具", description: "经典名言集合", color: "bg-indigo-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 84, name: "文案大全", icon: "📝", category: "文字工具", description: "各类文案素材", color: "bg-green-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 85, name: "WiFi昵称", icon: "📶", category: "文字工具", description: "创意WiFi名称", color: "bg-cyan-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 86, name: "特殊小尾巴", icon: "🎀", category: "文字工具", description: "文字装饰符号", color: "bg-pink-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 87, name: "小辫子昵称", icon: "🎭", category: "文字工具", description: "特殊字符昵称", color: "bg-violet-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 88, name: "文本逆序", icon: "🔄", category: "文字工具", description: "文字顺序颠倒", color: "bg-gray-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 89, name: "上标电话", icon: "☎️", category: "文字工具", description: "上标电话号码", color: "bg-blue-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 90, name: "下标电话", icon: "📞", category: "文字工具", description: "下标电话号码", color: "bg-teal-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 91, name: "尖叫文字", icon: "😱", category: "文字工具", description: "夸张文字效果", color: "bg-red-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 92, name: "小字母昵称", icon: "ᵃᵇᶜ", category: "文字工具", description: "小字母装饰", color: "bg-lime-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 93, name: "大小写转换", icon: "Aa", category: "文字工具", description: "英文大小写转换", color: "bg-blue-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 94, name: "爱心文字", icon: "💖", category: "文字工具", description: "爱心形状文字", color: "bg-pink-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 95, name: "文字九宫格", icon: "#️⃣", category: "文字工具", description: "九宫格文字排列", color: "bg-purple-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 96, name: "带框文字", icon: "🔲", category: "文字工具", description: "文字加边框", color: "bg-gray-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 97, name: "颜文字合集", icon: "(ﾟ∀ﾟ)", category: "文字工具", description: "各种表情符号", color: "bg-yellow-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 98, name: "删除线文字", icon: "̶T̶e̶x̶t̶", category: "文字工具", description: "删除线效果", color: "bg-slate-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 99, name: "爱心颜文字", icon: "♥", category: "文字工具", description: "爱心表情符号", color: "bg-rose-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 100, name: "520字", icon: "5️⃣2️⃣0️⃣", category: "文字工具", description: "520数字艺术", color: "bg-red-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 101, name: "箭头文字", icon: "➡️", category: "文字工具", description: "各种箭头符号", color: "bg-blue-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 102, name: "人间凑数的日子", icon: "📆", category: "文字工具", description: "佛系文案生成", color: "bg-amber-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 103, name: "坦克文字", icon: "🚗", category: "文字工具", description: "坦克ASCII艺术", color: "bg-green-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 104, name: "直升机文字", icon: "🚁", category: "文字工具", description: "飞机ASCII艺术", color: "bg-sky-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 105, name: "音乐文字", icon: "🎵", category: "文字工具", description: "音符符号文字", color: "bg-purple-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  { id: 106, name: "发疯语录", icon: "🤪", category: "文字工具", description: "搞怪文案集合", color: "bg-lime-100", isDisabled: true, isUnlocked: false, needsBackend: false },
  { id: 107, name: "爱情公寓语录", icon: "🏠", category: "文字工具", description: "经典台词集合", color: "bg-pink-100", isNew: true, isUnlocked: false, needsBackend: false },
  { id: 108, name: "随机情话", icon: "💕", category: "文字工具", description: "浪漫情话生成", color: "bg-rose-100", isInDevelopment: true, isUnlocked: false, needsBackend: false },
  // 图片壁纸 (9个)
  {
    id: 109,
    name: "随机头像",
    icon: "👤",
    category: "图片壁纸",
    description: "随机生成头像",
    color: "bg-indigo-100",
    isNew: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "random-avatar"
  },
  {
    id: 110,
    name: "随机加载图",
    icon: "🔄",
    category: "图片壁纸",
    description: "随机占位图片",
    color: "bg-gray-100",
    isInDevelopment: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "random-loading-image"
  },
  {
    id: 111,
    name: "Bing每日一图",
    icon: "🖼️",
    category: "图片壁纸",
    description: "Bing精美壁纸",
    color: "bg-teal-100",
    isDisabled: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "bing-daily-wallpaper"
  },
  {
    id: 112,
    name: "随机PC壁纸",
    icon: "🖥️",
    category: "图片壁纸",
    description: "电脑桌面壁纸",
    color: "bg-blue-100",
    isNew: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "random-pc-wallpaper"
  },
  { id: 115, name: "文字壁纸大全", icon: "📝", category: "图片壁纸", description: "文字背景壁纸", color: "bg-gray-100", isNew: true, isUnlocked: false, needsBackend: false },
  {
    id: 116,
    name: "动漫壁纸",
    icon: "🎭",
    category: "图片壁纸",
    description: "高清动漫壁纸",
    color: "bg-purple-100",
    isInDevelopment: true,
    isUnlocked: false,
    needsBackend: true,
    toolIdentifier: "anime-wallpapers"
  }
];
const categories = ["全部", "好玩推荐", "媒体工具", "颜色工具", "实用工具", "趣味工具", "程序员工具", "文字工具", "图片壁纸"];
function getToolsByCategory(category) {
  if (category === "全部") {
    return toolsData;
  }
  return toolsData.filter((tool) => tool.category === category);
}
function getToolById(id) {
  return toolsData.find((tool) => tool.id === parseInt(id));
}
function searchTools(query) {
  if (!query)
    return toolsData;
  return toolsData.filter(
    (tool) => tool.name.toLowerCase().includes(query.toLowerCase()) || tool.description.toLowerCase().includes(query.toLowerCase())
  );
}
exports.categories = categories;
exports.getToolById = getToolById;
exports.getToolsByCategory = getToolsByCategory;
exports.searchTools = searchTools;
exports.toolsData = toolsData;
//# sourceMappingURL=../../.sourcemap/mp-weixin/data/toolsData.js.map
