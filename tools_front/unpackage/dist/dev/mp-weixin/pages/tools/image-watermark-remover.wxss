/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-920616e1 {
  display: flex;
}
.flex-1.data-v-920616e1 {
  flex: 1;
}
.items-center.data-v-920616e1 {
  align-items: center;
}
.justify-center.data-v-920616e1 {
  justify-content: center;
}
.justify-between.data-v-920616e1 {
  justify-content: space-between;
}
.text-center.data-v-920616e1 {
  text-align: center;
}
.rounded.data-v-920616e1 {
  border-radius: 3px;
}
.rounded-lg.data-v-920616e1 {
  border-radius: 6px;
}
.shadow.data-v-920616e1 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-920616e1 {
  padding: 16rpx;
}
.m-4.data-v-920616e1 {
  margin: 16rpx;
}
.mb-4.data-v-920616e1 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-920616e1 {
  margin-top: 16rpx;
}
.min-h-screen.data-v-920616e1 {
  min-height: 100vh;
}
.bg-gray-50.data-v-920616e1 {
  background-color: #f9fafb;
}
.bg-white.data-v-920616e1 {
  background-color: #ffffff;
}
.shadow-sm.data-v-920616e1 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
.rounded-2xl.data-v-920616e1 {
  border-radius: 1rem;
}
.rounded-lg.data-v-920616e1 {
  border-radius: 0.5rem;
}
.rounded-md.data-v-920616e1 {
  border-radius: 0.375rem;
}
.rounded-full.data-v-920616e1 {
  border-radius: 9999px;
}
.rounded.data-v-920616e1 {
  border-radius: 0.25rem;
}
.p-4.data-v-920616e1 {
  padding: 1rem;
}
.p-6.data-v-920616e1 {
  padding: 1.5rem;
}
.p-8.data-v-920616e1 {
  padding: 2rem;
}
.p-1.data-v-920616e1 {
  padding: 0.25rem;
}
.px-2.data-v-920616e1 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.py-1.data-v-920616e1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.px-3.data-v-920616e1 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4.data-v-920616e1 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.py-2.data-v-920616e1 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.px-6.data-v-920616e1 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.py-6.data-v-920616e1 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.mr-2.data-v-920616e1 {
  margin-right: 0.5rem;
}
.mr-4.data-v-920616e1 {
  margin-right: 1rem;
}
.mb-2.data-v-920616e1 {
  margin-bottom: 0.5rem;
}
.mb-3.data-v-920616e1 {
  margin-bottom: 0.75rem;
}
.mb-4.data-v-920616e1 {
  margin-bottom: 1rem;
}
.mt-2.data-v-920616e1 {
  margin-top: 0.5rem;
}
.mx-auto.data-v-920616e1 {
  margin-left: auto;
  margin-right: auto;
}
.text-lg.data-v-920616e1 {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm.data-v-920616e1 {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xs.data-v-920616e1 {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-semibold.data-v-920616e1 {
  font-weight: 600;
}
.font-medium.data-v-920616e1 {
  font-weight: 500;
}
.text-gray-900.data-v-920616e1 {
  color: #111827;
}
.text-gray-700.data-v-920616e1 {
  color: #374151;
}
.text-gray-600.data-v-920616e1 {
  color: #4b5563;
}
.text-gray-500.data-v-920616e1 {
  color: #6b7280;
}
.text-gray-400.data-v-920616e1 {
  color: #9ca3af;
}
.text-white.data-v-920616e1 {
  color: #ffffff;
}
.text-blue-500.data-v-920616e1 {
  color: #3b82f6;
}
.bg-blue-500.data-v-920616e1 {
  background-color: #3b82f6;
}
.bg-blue-600.data-v-920616e1 {
  background-color: #2563eb;
}
.bg-red-500.data-v-920616e1 {
  background-color: #ef4444;
}
.bg-green-500.data-v-920616e1 {
  background-color: #10b981;
}
.bg-gray-200.data-v-920616e1 {
  background-color: #e5e7eb;
}
.bg-gray-400.data-v-920616e1 {
  background-color: #9ca3af;
}
.bg-black.data-v-920616e1 {
  background-color: #000000;
}
.bg-opacity-50.data-v-920616e1 {
  background-color: rgba(0, 0, 0, 0.5);
}
.border.data-v-920616e1 {
  border-width: 1px;
}
.border-2.data-v-920616e1 {
  border-width: 2px;
}
.border-gray-300.data-v-920616e1 {
  border-color: #d1d5db;
}
.border-dashed.data-v-920616e1 {
  border-style: dashed;
}
.hover-bg-blue-600.data-v-920616e1:hover {
  background-color: #2563eb;
}
.hover-bg-blue-700.data-v-920616e1:hover {
  background-color: #1d4ed8;
}
.hover-bg-red-600.data-v-920616e1:hover {
  background-color: #dc2626;
}
.hover-bg-gray-50.data-v-920616e1:hover {
  background-color: #f9fafb;
}
.disabled-bg-gray-400.data-v-920616e1:disabled {
  background-color: #9ca3af;
}
.disabled-cursor-not-allowed.data-v-920616e1:disabled {
  cursor: not-allowed;
}
.w-full.data-v-920616e1 {
  width: 100%;
}
.h-32.data-v-920616e1 {
  height: 8rem;
}
.h-2.data-v-920616e1 {
  height: 0.5rem;
}
.max-h-80.data-v-920616e1 {
  max-height: 20rem;
}
.flex.data-v-920616e1 {
  display: flex;
}
.grid.data-v-920616e1 {
  display: grid;
}
.hidden.data-v-920616e1 {
  display: none;
}
.grid-cols-2.data-v-920616e1 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.gap-3.data-v-920616e1 {
  gap: 0.75rem;
}
.flex-1.data-v-920616e1 {
  flex: 1 1 0%;
}
.items-center.data-v-920616e1 {
  align-items: center;
}
.justify-center.data-v-920616e1 {
  justify-content: center;
}
.justify-between.data-v-920616e1 {
  justify-content: space-between;
}
.text-center.data-v-920616e1 {
  text-align: center;
}
.object-cover.data-v-920616e1 {
  object-fit: cover;
}
.overflow-y-auto.data-v-920616e1 {
  overflow-y: auto;
}
.relative.data-v-920616e1 {
  position: relative;
}
.absolute.data-v-920616e1 {
  position: absolute;
}
.top-1.data-v-920616e1 {
  top: 0.25rem;
}
.right-1.data-v-920616e1 {
  right: 0.25rem;
}
.bottom-1.data-v-920616e1 {
  bottom: 0.25rem;
}
.left-1.data-v-920616e1 {
  left: 0.25rem;
}
.space-y-4.data-v-920616e1 {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}
.transition-colors.data-v-920616e1 {
  transition-property: color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all.data-v-920616e1 {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-300.data-v-920616e1 {
  transition-duration: 300ms;
}
button.data-v-920616e1 {
  cursor: pointer;
}
input[type=file].data-v-920616e1::-webkit-file-upload-button {
  cursor: pointer;
}
.group.data-v-920616e1 {
  position: relative;
}
.group:hover .group-hover-bg-opacity-40.data-v-920616e1 {
  background-color: rgba(0, 0, 0, 0.4);
}
.group:hover .group-hover-opacity-100.data-v-920616e1 {
  opacity: 1;
}
.group:hover .group-hover-translate-y-0.data-v-920616e1 {
  transform: translateY(0);
}
.bg-opacity-0.data-v-920616e1 {
  background-color: rgba(0, 0, 0, 0);
}
.bg-opacity-40.data-v-920616e1 {
  background-color: rgba(0, 0, 0, 0.4);
}
.opacity-0.data-v-920616e1 {
  opacity: 0;
}
.opacity-100.data-v-920616e1 {
  opacity: 1;
}
.translate-y-2.data-v-920616e1 {
  transform: translateY(0.5rem);
}
.translate-y-0.data-v-920616e1 {
  transform: translateY(0);
}
.transform.data-v-920616e1 {
  transform: translateY(0.5rem);
}
.shadow-lg.data-v-920616e1 {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
.fixed.data-v-920616e1 {
  position: fixed;
}
.inset-0.data-v-920616e1 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.z-50.data-v-920616e1 {
  z-index: 50;
}
.max-w-sm.data-v-920616e1 {
  max-width: 24rem;
}
.max-h-96.data-v-920616e1 {
  max-height: 24rem;
}
.object-contain.data-v-920616e1 {
  object-fit: contain;
}
.bg-opacity-90.data-v-920616e1 {
  background-color: rgba(0, 0, 0, 0.9);
}
.bg-opacity-70.data-v-920616e1 {
  background-color: rgba(0, 0, 0, 0.7);
}
.top-4.data-v-920616e1 {
  top: 1rem;
}
.right-4.data-v-920616e1 {
  right: 1rem;
}
.bottom-4.data-v-920616e1 {
  bottom: 1rem;
}
.left-4.data-v-920616e1 {
  left: 1rem;
}
.flex-col.data-v-920616e1 {
  flex-direction: column;
}
.gap-2.data-v-920616e1 {
  gap: 0.5rem;
}
.cursor-pointer.data-v-920616e1 {
  cursor: pointer;
}
.aspect-square.data-v-920616e1 {
  aspect-ratio: 1/1;
}
.active-scale-95.data-v-920616e1:active {
  transform: scale(0.95);
}
.hover-bg-blue-600.data-v-920616e1:hover {
  background-color: #2563eb;
}
.preview-modal.data-v-920616e1 {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999;
}
.preview-mask.data-v-920616e1 {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
}
.preview-content.data-v-920616e1 {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.preview-image.data-v-920616e1 {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.close-btn.data-v-920616e1 {
  position: fixed;
  right: 30rpx;
  top: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.close-icon.data-v-920616e1 {
  color: #fff;
  font-size: 48rpx;
}
.download-btn.data-v-920616e1 {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  background: #3b82f6;
  color: #fff;
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80%;
  max-width: 600rpx;
}
.download-text.data-v-920616e1 {
  color: #fff;
}