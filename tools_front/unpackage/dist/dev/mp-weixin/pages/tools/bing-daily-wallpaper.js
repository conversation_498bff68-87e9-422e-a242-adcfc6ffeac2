"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const utils_index = require("../../utils/index.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  (_component_path + _component_polyline + _component_line + _component_svg + _component_circle)();
}
const _sfc_main = {
  __name: "bing-daily-wallpaper",
  setup(__props) {
    const toolService = new utils_toolService.ToolService();
    const currentWallpaper = common_vendor.ref(null);
    const recentWallpapers = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const showDownloadMenu = common_vendor.ref(false);
    const downloadWallpaperTarget = common_vendor.ref(null);
    const showDetail = common_vendor.ref(false);
    const detailWallpaper = common_vendor.ref(null);
    const resolutions = common_vendor.computed(() => {
      return [
        "1920x1080",
        "2560x1440",
        "3840x2160"
      ];
    });
    const usageTips = common_vendor.computed(() => {
      return [
        "本工具每天自动更新Bing搜索引擎的每日精选壁纸。",
        "您可以点击“刷新”按钮手动更新。",
        "点击壁纸图片可以查看详情。",
        "点击“下载”按钮可以将壁纸保存到您的相册。"
      ];
    });
    common_vendor.onMounted(() => {
      fetchBingWallpaper();
    });
    const fetchBingWallpaper = async () => {
      loading.value = true;
      try {
        const result = await toolService.getBingDailyWallpaper();
        if (result.code === 200 && result.data && result.data.success) {
          const wallpaperData = result.data;
          currentWallpaper.value = {
            id: Date.now(),
            title: wallpaperData.title || "Bing每日壁纸",
            description: extractLocationFromTitle(wallpaperData.title),
            url: wallpaperData.url,
            date: wallpaperData.time || (/* @__PURE__ */ new Date()).toISOString().split("T")[0],
            copyright: wallpaperData.title || "",
            copyrightlink: wallpaperData.link || "https://www.bing.com",
            location: extractLocationFromTitle(wallpaperData.title),
            tags: ["Bing", "每日壁纸", "精选"]
          };
          utils_index.showSuccess("Bing每日壁纸加载成功！");
        } else {
          throw new Error(result.message || "获取壁纸失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/bing-daily-wallpaper.vue:236", "获取Bing每日壁纸失败:", error);
        try {
          await simulateWallpaperGeneration();
          utils_index.showSuccess("Bing每日壁纸加载成功！（本地处理）");
        } catch (localError) {
          utils_index.showError("壁纸加载失败，请重试");
        }
      } finally {
        loading.value = false;
      }
    };
    const extractLocationFromTitle = (title) => {
      if (!title)
        return "未知位置";
      const locationMatch = title.match(/，([^，(]+)(?:\s*\([^)]*\))?$/);
      if (locationMatch) {
        return locationMatch[1].trim();
      }
      return title.split("，")[0] || title.substring(0, 20) + "...";
    };
    const simulateWallpaperGeneration = async () => {
      return new Promise((resolve) => {
        setTimeout(() => {
          const randomId = Date.now();
          const today = (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
          currentWallpaper.value = {
            id: randomId,
            title: `Bing每日壁纸 ${today}`,
            description: "Bing搜索引擎每日精选壁纸",
            url: `https://picsum.photos/1920/1080?random=${randomId}`,
            date: today,
            copyright: "© Bing 每日壁纸",
            copyrightlink: "https://www.bing.com",
            location: "未知位置",
            tags: ["Bing", "每日壁纸", "精选"]
          };
          resolve();
        }, 1e3);
      });
    };
    const refreshWallpapers = () => {
      fetchBingWallpaper();
    };
    const closeDownloadMenu = () => {
      showDownloadMenu.value = false;
      downloadWallpaperTarget.value = null;
    };
    const confirmDownload = (resolution) => {
      if (downloadWallpaperTarget.value) {
        downloadWallpaper(downloadWallpaperTarget.value, resolution);
      }
      closeDownloadMenu();
    };
    const downloadWallpaper = async (wallpaper, resolution = "1920x1080") => {
      utils_index.showLoading("下载中...");
      try {
        common_vendor.index.downloadFile({
          url: wallpaper.url,
          success: (res) => {
            if (res.statusCode === 200) {
              common_vendor.index.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  utils_index.showSuccess("已保存到相册！");
                },
                fail: (error) => {
                  utils_index.showError("保存失败：" + error.errMsg);
                  common_vendor.index.__f__("error", "at pages/tools/bing-daily-wallpaper.vue:335", "保存失败:", error);
                }
              });
            } else {
              utils_index.showError("下载失败");
            }
          },
          fail: (error) => {
            utils_index.showError("下载失败：" + error.errMsg);
            common_vendor.index.__f__("error", "at pages/tools/bing-daily-wallpaper.vue:344", "下载失败:", error);
          }
        });
      } catch (error) {
        utils_index.showError("下载失败：" + error.message);
        common_vendor.index.__f__("error", "at pages/tools/bing-daily-wallpaper.vue:349", "下载失败:", error);
      } finally {
        utils_index.hideLoading();
      }
    };
    const showWallpaperDetail = (wallpaper) => {
      detailWallpaper.value = wallpaper;
      showDetail.value = true;
    };
    const closeDetail = () => {
      showDetail.value = false;
      detailWallpaper.value = null;
    };
    const getResolutionDesc = (resolution) => {
      const descriptions = {
        "1920x1080": "全高清",
        "2560x1440": "2K",
        "3840x2160": "4K"
      };
      return descriptions[resolution] || "";
    };
    const onImageError = (e) => {
      common_vendor.index.__f__("error", "at pages/tools/bing-daily-wallpaper.vue:379", "图片加载失败:", e);
    };
    const onLocationClick = (location) => {
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: currentWallpaper.value
      }, currentWallpaper.value ? {
        b: common_vendor.t(currentWallpaper.value.date),
        c: common_vendor.o(refreshWallpapers),
        d: loading.value,
        e: currentWallpaper.value.url,
        f: currentWallpaper.value.title,
        g: common_vendor.o(onImageError),
        h: common_vendor.o(($event) => showWallpaperDetail(currentWallpaper.value)),
        i: common_vendor.p({
          d: "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"
        }),
        j: common_vendor.p({
          points: "7 10 12 15 17 10"
        }),
        k: common_vendor.p({
          x1: "12",
          y1: "15",
          x2: "12",
          y2: "3"
        }),
        l: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "32",
          height: "32",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2.4",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        m: common_vendor.o(($event) => downloadWallpaper(currentWallpaper.value)),
        n: common_vendor.t(currentWallpaper.value.title),
        o: common_vendor.t(currentWallpaper.value.description),
        p: common_vendor.p({
          cx: "12",
          cy: "10",
          r: "3"
        }),
        q: common_vendor.p({
          d: "M12 2a8 8 0 0 1 8 8c0 7-8 12-8 12S4 17 4 10a8 8 0 0 1 8-8z"
        }),
        r: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "18",
          height: "18",
          fill: "none",
          stroke: "#1976d2",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        s: common_vendor.t(currentWallpaper.value.location),
        t: common_vendor.o(($event) => onLocationClick(currentWallpaper.value.location))
      } : {}, {
        v: common_vendor.f(recentWallpapers.value.slice(1), (wallpaper, index, i0) => {
          return {
            a: wallpaper.url,
            b: wallpaper.title,
            c: common_vendor.o(onImageError, index),
            d: common_vendor.t(wallpaper.title),
            e: common_vendor.t(wallpaper.date),
            f: "b1ea4072-8-" + i0 + "," + ("b1ea4072-7-" + i0),
            g: "b1ea4072-9-" + i0 + "," + ("b1ea4072-7-" + i0),
            h: "b1ea4072-7-" + i0,
            i: common_vendor.t(wallpaper.location),
            j: common_vendor.o(($event) => onLocationClick(wallpaper.location), index),
            k: "b1ea4072-11-" + i0 + "," + ("b1ea4072-10-" + i0),
            l: "b1ea4072-12-" + i0 + "," + ("b1ea4072-10-" + i0),
            m: "b1ea4072-13-" + i0 + "," + ("b1ea4072-10-" + i0),
            n: "b1ea4072-10-" + i0,
            o: common_vendor.o(($event) => downloadWallpaper(wallpaper, "1920x1080"), index),
            p: index,
            q: common_vendor.o(($event) => showWallpaperDetail(wallpaper), index)
          };
        }),
        w: common_vendor.p({
          cx: "12",
          cy: "10",
          r: "3"
        }),
        x: common_vendor.p({
          d: "M12 2a8 8 0 0 1 8 8c0 7-8 12-8 12S4 17 4 10a8 8 0 0 1 8-8z"
        }),
        y: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16",
          fill: "none",
          stroke: "#1976d2",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        z: common_vendor.p({
          d: "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"
        }),
        A: common_vendor.p({
          points: "7 10 12 15 17 10"
        }),
        B: common_vendor.p({
          x1: "12",
          y1: "15",
          x2: "12",
          y2: "3"
        }),
        C: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "36",
          height: "36",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2.4",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        D: common_vendor.f(usageTips.value, (tip, index, i0) => {
          return {
            a: common_vendor.t(tip),
            b: index
          };
        }),
        E: showDetail.value
      }, showDetail.value ? {
        F: common_vendor.t(detailWallpaper.value.title),
        G: common_vendor.o(closeDetail),
        H: detailWallpaper.value.url,
        I: detailWallpaper.value.title,
        J: common_vendor.t(detailWallpaper.value.description),
        K: common_vendor.p({
          cx: "12",
          cy: "10",
          r: "3"
        }),
        L: common_vendor.p({
          d: "M12 2a8 8 0 0 1 8 8c0 7-8 12-8 12S4 17 4 10a8 8 0 0 1 8-8z"
        }),
        M: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16",
          fill: "none",
          stroke: "#1976d2",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        N: common_vendor.t(detailWallpaper.value.location),
        O: common_vendor.o(($event) => onLocationClick(detailWallpaper.value.location)),
        P: common_vendor.t(detailWallpaper.value.date),
        Q: common_vendor.f(resolutions.value, (resolution, k0, i0) => {
          return {
            a: "b1ea4072-18-" + i0 + "," + ("b1ea4072-17-" + i0),
            b: "b1ea4072-19-" + i0 + "," + ("b1ea4072-17-" + i0),
            c: "b1ea4072-20-" + i0 + "," + ("b1ea4072-17-" + i0),
            d: "b1ea4072-17-" + i0,
            e: common_vendor.t(resolution),
            f: resolution,
            g: common_vendor.o(($event) => downloadWallpaper(detailWallpaper.value, resolution), resolution)
          };
        }),
        R: common_vendor.p({
          d: "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"
        }),
        S: common_vendor.p({
          points: "7 10 12 15 17 10"
        }),
        T: common_vendor.p({
          x1: "12",
          y1: "15",
          x2: "12",
          y2: "3"
        }),
        U: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "22",
          height: "22",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        V: common_vendor.o(closeDetail)
      } : {}, {
        W: showDownloadMenu.value
      }, showDownloadMenu.value ? {
        X: common_vendor.o(closeDownloadMenu),
        Y: common_vendor.f(resolutions.value, (resolution, k0, i0) => {
          return {
            a: common_vendor.t(resolution),
            b: common_vendor.t(getResolutionDesc(resolution)),
            c: resolution,
            d: common_vendor.o(($event) => confirmDownload(resolution), resolution)
          };
        }),
        Z: common_vendor.o(closeDownloadMenu)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b1ea4072"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/bing-daily-wallpaper.js.map
