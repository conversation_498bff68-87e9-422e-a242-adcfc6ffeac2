<view class="bing-wallpaper-page data-v-b1ea4072"><view class="content data-v-b1ea4072"><view wx:if="{{a}}" class="wallpaper-card main-card data-v-b1ea4072"><view class="card-header data-v-b1ea4072"><text class="card-icon data-v-b1ea4072">📅</text><text class="card-title data-v-b1ea4072">今日壁纸 - {{b}}</text><button class="refresh-btn-minimal data-v-b1ea4072" bindtap="{{c}}" disabled="{{d}}"><text class="refresh-btn-text data-v-b1ea4072">刷新</text></button></view><view class="card-content data-v-b1ea4072"><view class="wallpaper-container data-v-b1ea4072"><image src="{{e}}" alt="{{f}}" class="wallpaper-image data-v-b1ea4072" mode="aspectFill" binderror="{{g}}" bindtap="{{h}}"/><button class="download-btn-minimal data-v-b1ea4072" catchtap="{{m}}"><svg wx:if="{{l}}" u-s="{{['d']}}" class="download-svg data-v-b1ea4072" u-i="b1ea4072-0" bind:__l="__l" u-p="{{l}}"><path wx:if="{{i}}" class="data-v-b1ea4072" u-i="b1ea4072-1,b1ea4072-0" bind:__l="__l" u-p="{{i}}"/><polyline wx:if="{{j}}" class="data-v-b1ea4072" u-i="b1ea4072-2,b1ea4072-0" bind:__l="__l" u-p="{{j}}"/><line wx:if="{{k}}" class="data-v-b1ea4072" u-i="b1ea4072-3,b1ea4072-0" bind:__l="__l" u-p="{{k}}"/></svg></button></view><view class="wallpaper-info data-v-b1ea4072"><text class="wallpaper-title data-v-b1ea4072">{{n}}</text><text class="wallpaper-description data-v-b1ea4072">{{o}}</text><view class="wallpaper-location data-v-b1ea4072"><svg wx:if="{{r}}" u-s="{{['d']}}" class="location-svg data-v-b1ea4072" u-i="b1ea4072-4" bind:__l="__l" u-p="{{r}}"><circle wx:if="{{p}}" class="data-v-b1ea4072" u-i="b1ea4072-5,b1ea4072-4" bind:__l="__l" u-p="{{p}}"/><path wx:if="{{q}}" class="data-v-b1ea4072" u-i="b1ea4072-6,b1ea4072-4" bind:__l="__l" u-p="{{q}}"/></svg><text class="location-link data-v-b1ea4072" bindtap="{{t}}">{{s}}</text></view></view></view></view><view class="wallpaper-card recent-card data-v-b1ea4072"><view class="card-header data-v-b1ea4072"><text class="card-title data-v-b1ea4072">最近壁纸</text></view><view class="card-content data-v-b1ea4072"><view class="recent-list data-v-b1ea4072"><view wx:for="{{v}}" wx:for-item="wallpaper" wx:key="p" class="recent-item data-v-b1ea4072" bindtap="{{wallpaper.q}}"><image src="{{wallpaper.a}}" alt="{{wallpaper.b}}" class="recent-image data-v-b1ea4072" mode="aspectFill" binderror="{{wallpaper.c}}"/><view class="recent-info data-v-b1ea4072"><text class="recent-title data-v-b1ea4072">{{wallpaper.d}}</text><text class="recent-date data-v-b1ea4072">{{wallpaper.e}}</text><view class="recent-location data-v-b1ea4072"><svg wx:if="{{y}}" u-s="{{['d']}}" class="location-svg data-v-b1ea4072" u-i="{{wallpaper.h}}" bind:__l="__l" u-p="{{y}}"><circle wx:if="{{w}}" class="data-v-b1ea4072" u-i="{{wallpaper.f}}" bind:__l="__l" u-p="{{w}}"/><path wx:if="{{x}}" class="data-v-b1ea4072" u-i="{{wallpaper.g}}" bind:__l="__l" u-p="{{x}}"/></svg><text class="location-link data-v-b1ea4072" catchtap="{{wallpaper.j}}">{{wallpaper.i}}</text></view></view><button catchtap="{{wallpaper.o}}" class="download-btn-strong data-v-b1ea4072" aria-label="下载"><svg wx:if="{{C}}" u-s="{{['d']}}" class="download-svg data-v-b1ea4072" u-i="{{wallpaper.n}}" bind:__l="__l" u-p="{{C}}"><path wx:if="{{z}}" class="data-v-b1ea4072" u-i="{{wallpaper.k}}" bind:__l="__l" u-p="{{z}}"/><polyline wx:if="{{A}}" class="data-v-b1ea4072" u-i="{{wallpaper.l}}" bind:__l="__l" u-p="{{A}}"/><line wx:if="{{B}}" class="data-v-b1ea4072" u-i="{{wallpaper.m}}" bind:__l="__l" u-p="{{B}}"/></svg></button></view></view></view></view><view class="wallpaper-card usage-card data-v-b1ea4072"><view class="card-header data-v-b1ea4072"><text class="card-title data-v-b1ea4072">使用说明</text></view><view class="card-content data-v-b1ea4072"><view class="usage-list data-v-b1ea4072"><view wx:for="{{D}}" wx:for-item="tip" wx:key="b" class="usage-item data-v-b1ea4072"><text class="usage-bullet data-v-b1ea4072">•</text><text class="usage-text data-v-b1ea4072">{{tip.a}}</text></view></view></view></view></view><view wx:if="{{E}}" class="detail-mask data-v-b1ea4072" bindtap="{{V}}"><view class="detail-dialog data-v-b1ea4072"><view class="detail-header data-v-b1ea4072"><text class="detail-title data-v-b1ea4072">{{F}}</text><text class="detail-close data-v-b1ea4072" bindtap="{{G}}">×</text></view><view class="detail-content data-v-b1ea4072"><view class="detail-main-content data-v-b1ea4072"><image src="{{H}}" alt="{{I}}" class="detail-image data-v-b1ea4072" mode="aspectFit"/><view class="detail-info data-v-b1ea4072"><text class="detail-description data-v-b1ea4072">{{J}}</text><view class="detail-location data-v-b1ea4072"><svg wx:if="{{M}}" u-s="{{['d']}}" class="location-svg data-v-b1ea4072" u-i="b1ea4072-14" bind:__l="__l" u-p="{{M}}"><circle wx:if="{{K}}" class="data-v-b1ea4072" u-i="b1ea4072-15,b1ea4072-14" bind:__l="__l" u-p="{{K}}"/><path wx:if="{{L}}" class="data-v-b1ea4072" u-i="b1ea4072-16,b1ea4072-14" bind:__l="__l" u-p="{{L}}"/></svg><text class="location-link data-v-b1ea4072" catchtap="{{O}}">{{N}}</text></view><text class="detail-date data-v-b1ea4072">{{P}}</text></view></view><view class="detail-actions data-v-b1ea4072"><button wx:for="{{Q}}" wx:for-item="resolution" wx:key="f" bindtap="{{resolution.g}}" class="detail-download-btn data-v-b1ea4072"><svg wx:if="{{U}}" u-s="{{['d']}}" class="download-svg data-v-b1ea4072" u-i="{{resolution.d}}" bind:__l="__l" u-p="{{U}}"><path wx:if="{{R}}" class="data-v-b1ea4072" u-i="{{resolution.a}}" bind:__l="__l" u-p="{{R}}"/><polyline wx:if="{{S}}" class="data-v-b1ea4072" u-i="{{resolution.b}}" bind:__l="__l" u-p="{{S}}"/><line wx:if="{{T}}" class="data-v-b1ea4072" u-i="{{resolution.c}}" bind:__l="__l" u-p="{{T}}"/></svg><text class="download-text data-v-b1ea4072">{{resolution.e}}</text></button></view></view></view></view><view wx:if="{{W}}" class="download-mask data-v-b1ea4072" bindtap="{{Z}}"><view class="download-dialog data-v-b1ea4072"><view class="download-header data-v-b1ea4072"><text class="download-title data-v-b1ea4072">选择下载分辨率</text><text class="download-close data-v-b1ea4072" bindtap="{{X}}">×</text></view><view class="download-content data-v-b1ea4072"><button wx:for="{{Y}}" wx:for-item="resolution" wx:key="c" bindtap="{{resolution.d}}" class="resolution-btn data-v-b1ea4072"><text class="resolution-text data-v-b1ea4072">{{resolution.a}}</text><text class="resolution-desc data-v-b1ea4072">{{resolution.b}}</text></button></view></view></view></view>