/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-b1ea4072 {
  display: flex;
}
.flex-1.data-v-b1ea4072 {
  flex: 1;
}
.items-center.data-v-b1ea4072 {
  align-items: center;
}
.justify-center.data-v-b1ea4072 {
  justify-content: center;
}
.justify-between.data-v-b1ea4072 {
  justify-content: space-between;
}
.text-center.data-v-b1ea4072 {
  text-align: center;
}
.rounded.data-v-b1ea4072 {
  border-radius: 3px;
}
.rounded-lg.data-v-b1ea4072 {
  border-radius: 6px;
}
.shadow.data-v-b1ea4072 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-b1ea4072 {
  padding: 16rpx;
}
.m-4.data-v-b1ea4072 {
  margin: 16rpx;
}
.mb-4.data-v-b1ea4072 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-b1ea4072 {
  margin-top: 16rpx;
}
.bing-wallpaper-page.data-v-b1ea4072 {
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
}
.content.data-v-b1ea4072 {
  flex: 1;
  padding: 32rpx;
  background: #f8f9fa;
}
.wallpaper-card.data-v-b1ea4072 {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.wallpaper-card.data-v-b1ea4072:hover {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-2rpx);
}
.card-header.data-v-b1ea4072 {
  padding: 32rpx 32rpx 0;
  display: flex;
  align-items: center;
  gap: 12rpx;
  justify-content: space-between;
}
.card-icon.data-v-b1ea4072 {
  font-size: 28rpx;
  color: #ff6b35;
}
.card-title.data-v-b1ea4072 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.card-content.data-v-b1ea4072 {
  padding: 24rpx 32rpx 32rpx;
}
.main-card .wallpaper-container.data-v-b1ea4072 {
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  aspect-ratio: 16/9;
  background: #f8f9fa;
}
.main-card .wallpaper-image.data-v-b1ea4072 {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.main-card .wallpaper-overlay.data-v-b1ea4072 {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.main-card .wallpaper-overlay.data-v-b1ea4072:hover {
  opacity: 1;
}
.main-card .download-options.data-v-b1ea4072 {
  display: flex;
  gap: 16rpx;
}
.main-card .download-btn.data-v-b1ea4072 {
  background: rgba(255, 255, 255, 0.9);
  color: #1a1a1a;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8rpx;
  transition: all 0.15s;
  -webkit-backdrop-filter: blur(8rpx);
          backdrop-filter: blur(8rpx);
}
.main-card .download-btn.data-v-b1ea4072:hover {
  background: #ffffff;
  transform: translateY(-2rpx);
}
.main-card .download-icon.data-v-b1ea4072 {
  font-size: 24rpx;
}
.main-card .download-text.data-v-b1ea4072 {
  font-size: 24rpx;
  font-weight: 500;
}
.main-card .wallpaper-info.data-v-b1ea4072 {
  margin-top: 24rpx;
}
.main-card .wallpaper-title.data-v-b1ea4072 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  display: block;
  margin-bottom: 12rpx;
}
.main-card .wallpaper-description.data-v-b1ea4072 {
  font-size: 28rpx;
  color: #6c757d;
  line-height: 1.5;
  display: block;
  margin-bottom: 16rpx;
}
.main-card .wallpaper-location.data-v-b1ea4072 {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.main-card .location-icon.data-v-b1ea4072 {
  font-size: 24rpx;
  color: #007bff;
}
.main-card .location-text.data-v-b1ea4072 {
  font-size: 26rpx;
  color: #007bff;
  font-weight: 500;
}
.recent-card .recent-list.data-v-b1ea4072 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.recent-card .recent-item.data-v-b1ea4072 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid #f0f0f0;
  transition: all 0.15s;
  cursor: pointer;
  background: #fff;
  position: relative;
}
.recent-card .recent-item.data-v-b1ea4072:hover {
  background: #f8f9fa;
  border-color: #dee2e6;
}
.recent-card .recent-item.data-v-b1ea4072:active {
  transform: scale(0.98);
}
.recent-card .recent-image.data-v-b1ea4072 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  object-fit: cover;
  background: #f8f9fa;
  flex-shrink: 0;
}
.recent-card .recent-info.data-v-b1ea4072 {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.recent-card .recent-title.data-v-b1ea4072 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.recent-card .recent-date.data-v-b1ea4072 {
  font-size: 24rpx;
  color: #6c757d;
}
.recent-card .recent-location.data-v-b1ea4072 {
  display: flex;
  align-items: center;
  gap: 6rpx;
  margin-top: 2rpx;
}
.recent-card .download-btn-minimal.data-v-b1ea4072 {
  margin-left: auto;
}
.usage-card.data-v-b1ea4072 {
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
}
.usage-card .usage-list.data-v-b1ea4072 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.usage-card .usage-item.data-v-b1ea4072 {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
}
.usage-card .usage-bullet.data-v-b1ea4072 {
  color: #6c757d;
  font-size: 24rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
}
.usage-card .usage-text.data-v-b1ea4072 {
  font-size: 28rpx;
  color: #495057;
  line-height: 1.5;
  flex: 1;
}
.detail-mask.data-v-b1ea4072, .download-mask.data-v-b1ea4072 {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(4rpx);
          backdrop-filter: blur(4rpx);
}
.detail-dialog.data-v-b1ea4072 {
  border-radius: 20rpx;
  box-shadow: 0 12rpx 40rpx rgba(25, 118, 210, 0.1), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  background: #fff;
  max-width: 92vw;
  padding: 0;
}
.detail-header.data-v-b1ea4072 {
  padding: 36rpx 40rpx 0 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fafbfc;
}
.detail-title.data-v-b1ea4072 {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
}
.detail-close.data-v-b1ea4072 {
  font-size: 40rpx;
  color: #b0b8c7;
  font-weight: 400;
  cursor: pointer;
  transition: color 0.15s;
}
.detail-close.data-v-b1ea4072:hover {
  color: #1976d2;
}
.detail-content.data-v-b1ea4072 {
  padding: 40rpx 0 32rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.detail-main-content.data-v-b1ea4072 {
  max-width: 520rpx;
  width: 100%;
  margin: 0 auto;
}
.detail-image.data-v-b1ea4072 {
  width: 100%;
  max-width: 520rpx;
  max-height: 320rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.08);
  background: #f6f8fa;
  object-fit: cover;
  margin-bottom: 32rpx;
}
.detail-info.data-v-b1ea4072 {
  margin-bottom: 32rpx;
  text-align: left;
}
.detail-description.data-v-b1ea4072 {
  font-size: 28rpx;
  color: #5a6270;
  line-height: 1.7;
  margin-bottom: 18rpx;
}
.detail-location.data-v-b1ea4072 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}
.location-link.data-v-b1ea4072 {
  font-size: 22rpx;
  color: #1976d2;
  font-weight: 400;
  margin-left: 2rpx;
  cursor: pointer;
  text-decoration: none;
}
.location-link.data-v-b1ea4072:hover {
  text-decoration: underline;
  color: #1251a3;
}
.detail-date.data-v-b1ea4072 {
  font-size: 22rpx;
  color: #b0b8c7;
  margin-bottom: 0;
}
.detail-actions.data-v-b1ea4072 {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 12rpx;
  justify-content: center;
  margin-top: 12rpx;
  overflow-x: hidden;
  padding: 0 32rpx 8rpx 32rpx;
  margin-left: 0;
  margin-right: 0;
}
.detail-download-btn.data-v-b1ea4072 {
  flex: 1 1 0;
  min-width: 0;
  height: 64rpx;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 16rpx;
  border: 2rpx solid #e3eaf5;
  background: #fff;
  color: #1976d2;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  transition: all 0.18s cubic-bezier(0.2, 0, 0.1, 1);
  box-shadow: 0 2rpx 8rpx rgba(25, 118, 210, 0.06);
  margin: 0;
  padding: 0 12rpx;
}
.download-svg.data-v-b1ea4072 {
  width: 32rpx;
  height: 32rpx;
  stroke: currentColor;
  display: block;
  stroke-width: 2.2;
}
.download-btn-strong.data-v-b1ea4072 {
  background: rgba(25, 118, 210, 0.1);
  border: 1rpx solid #e3eaf5;
  box-shadow: none;
  padding: 0;
  margin: 0 24rpx 0 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: color 0.15s, background 0.18s cubic-bezier(0.2, 0, 0.1, 1), box-shadow 0.18s cubic-bezier(0.2, 0, 0.1, 1), border-color 0.18s cubic-bezier(0.2, 0, 0.1, 1), transform 0.18s cubic-bezier(0.2, 0, 0.1, 1);
  color: #1565c0;
  border-radius: 50%;
  width: 56rpx;
  height: 56rpx;
  position: relative;
}
.download-btn-strong.data-v-b1ea4072:hover {
  background: rgba(25, 118, 210, 0.18);
  color: #0d3570;
  border-color: #b6d0f7;
  box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.13);
  transform: scale(1.1);
}
.download-btn-strong.data-v-b1ea4072:active {
  color: #0a2540;
  background: rgba(25, 118, 210, 0.22);
  border-color: #90b4e8;
  transform: scale(0.97);
}
.download-svg.data-v-b1ea4072 {
  width: 36rpx;
  height: 36rpx;
  stroke: currentColor;
  display: block;
  stroke-width: 2.4;
}
@media (max-width: 750rpx) {
.content.data-v-b1ea4072 {
    padding: 24rpx;
}
.card-header.data-v-b1ea4072 {
    padding: 24rpx 24rpx 0;
}
.card-content.data-v-b1ea4072 {
    padding: 16rpx 24rpx 24rpx;
}
.download-options.data-v-b1ea4072 {
    flex-direction: column;
    gap: 12rpx;
}
.recent-item.data-v-b1ea4072 {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
}
.recent-image.data-v-b1ea4072 {
    width: 100%;
    height: 200rpx;
}
.detail-actions.data-v-b1ea4072 {
    /* flex-direction: column; */
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 24rpx;
    justify-content: center;
}
.detail-download-btn.data-v-b1ea4072 {
    width: 100%;
    justify-content: center;
}
}
.refresh-btn-minimal.data-v-b1ea4072 {
  background: none;
  border: none;
  box-shadow: none;
  padding: 0 16rpx;
  margin-right: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56rpx;
  min-width: 72rpx;
  cursor: pointer;
  color: #222;
  border-radius: 8rpx;
  font-size: 32rpx;
  transition: color 0.15s, transform 0.15s;
}
.refresh-btn-minimal.data-v-b1ea4072:hover {
  color: #1976d2;
  transform: scale(1.06);
}
.refresh-btn-minimal.data-v-b1ea4072:active {
  color: #1251a3;
  transform: scale(0.97);
}
.refresh-btn-text.data-v-b1ea4072 {
  font-size: 32rpx;
  font-weight: 500;
  color: inherit;
  line-height: 56rpx;
  padding: 0;
  margin: 0;
  position: static;
}