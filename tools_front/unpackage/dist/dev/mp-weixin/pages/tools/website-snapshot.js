"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const utils_index = require("../../utils/index.js");
const _sfc_main = {
  __name: "website-snapshot",
  setup(__props) {
    const url = common_vendor.ref("");
    const device = common_vendor.ref("desktop");
    const selectedFormat = common_vendor.ref("png");
    const quality = common_vendor.ref(90);
    const delay = common_vendor.ref(2);
    const fullPage = common_vendor.ref(false);
    const removeAds = common_vendor.ref(false);
    const isGenerating = common_vendor.ref(false);
    const progress = common_vendor.ref(0);
    const previewImage = common_vendor.ref("");
    const history = common_vendor.ref([]);
    const devices = common_vendor.reactive([
      { id: "desktop", name: "桌面端", size: "1920×1080", icon: "💻" },
      { id: "tablet", name: "平板端", size: "768×1024", icon: "📱" },
      { id: "mobile", name: "手机端", size: "375×667", icon: "📱" }
    ]);
    const formats = common_vendor.reactive(["png", "jpg", "webp"]);
    const deviceName = common_vendor.computed(() => {
      const foundDevice = devices.find((d) => d.id === device.value);
      return foundDevice ? foundDevice.name : "桌面端";
    });
    common_vendor.onMounted(() => {
      const savedHistory = common_vendor.index.getStorageSync("website_snapshot_history");
      if (savedHistory) {
        history.value = savedHistory;
      }
    });
    const onUrlChange = (e) => {
      url.value = e.detail.value;
    };
    const pasteUrl = async () => {
      try {
        const clipboardData = await common_vendor.index.getClipboardData();
        if (clipboardData.data && isValidUrl(clipboardData.data)) {
          let pastedUrl = clipboardData.data.trim();
          if (!pastedUrl.startsWith("http://") && !pastedUrl.startsWith("https://")) {
            pastedUrl = "https://" + pastedUrl;
          }
          url.value = pastedUrl;
          common_vendor.index.showToast({
            title: "URL已粘贴",
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: "剪贴板中无有效URL",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "粘贴失败",
          icon: "none"
        });
      }
    };
    const clearUrl = () => {
      url.value = "";
      common_vendor.index.showToast({
        title: "URL已清空",
        icon: "success"
      });
    };
    const validateUrl = () => {
      if (isValidUrl(url.value)) {
        common_vendor.index.showToast({
          title: "URL格式正确",
          icon: "success"
        });
      } else {
        common_vendor.index.showToast({
          title: "URL格式错误",
          icon: "none"
        });
      }
    };
    const selectDevice = (deviceId) => {
      device2.value = deviceId;
      const device2 = devices.find((d) => d.id === deviceId);
      common_vendor.index.showToast({
        title: `已选择${device2.name}`,
        icon: "success"
      });
    };
    const selectFormat = (format) => {
      selectedFormat.value = format;
      common_vendor.index.showToast({
        title: `已选择${format.toUpperCase()}格式`,
        icon: "success"
      });
    };
    const onQualityChange = (e) => {
      quality.value = e.detail.value;
    };
    const onDelayChange = (e) => {
      delay.value = e.detail.value;
    };
    const onFullPageChange = (e) => {
      fullPage.value = e.detail.value;
    };
    const onRemoveAdsChange = (e) => {
      removeAds.value = e.detail.value;
    };
    const handleGenerate = async () => {
      if (!url.value || !isValidUrl(url.value)) {
        utils_index.showError("请输入有效的网站地址");
        return;
      }
      try {
        isGenerating.value = true;
        progress.value = 0;
        let finalUrl = url.value;
        if (!finalUrl.startsWith("http://") && !finalUrl.startsWith("https://")) {
          finalUrl = "https://" + finalUrl;
        }
        const progressInterval = setInterval(() => {
          if (progress.value < 80) {
            progress.value += Math.random() * 20;
          }
        }, 500);
        const result = await utils_toolService.toolService.generateWebsiteSnapshot({
          url: finalUrl,
          device: device.value,
          format: selectedFormat.value,
          quality: quality.value,
          delay: delay.value,
          fullPage: fullPage.value,
          removeAds: removeAds.value
        });
        clearInterval(progressInterval);
        progress.value = 100;
        if (result.success) {
          previewImage.value = result.data.snapshotUrl;
          utils_index.showSuccess("网站快照生成成功！");
          const historyItem = {
            id: Date.now(),
            url: url.value,
            image: result.data.snapshotUrl,
            device: deviceName.value,
            format: selectedFormat.value,
            timestamp: (/* @__PURE__ */ new Date()).toLocaleString()
          };
          history.value.unshift(historyItem);
          common_vendor.index.setStorageSync("website_snapshot_history", history.value);
        } else {
          throw new Error(result.message || "生成失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/website-snapshot.vue:437", "网站快照生成失败:", error);
        try {
          await simulateSnapshotGeneration();
          utils_index.showSuccess("网站快照生成成功！（本地处理）");
        } catch (localError) {
          utils_index.showError("网站快照生成失败，请重试");
        }
      } finally {
        isGenerating.value = false;
        progress.value = 0;
      }
    };
    const simulateSnapshotGeneration = async () => {
      return new Promise((resolve) => {
        setTimeout(() => {
          previewImage.value = `https://picsum.photos/800/600?random=${Date.now()}`;
          const historyItem = {
            id: Date.now(),
            url: url.value,
            image: previewImage.value,
            device: deviceName.value,
            format: selectedFormat.value,
            timestamp: (/* @__PURE__ */ new Date()).toLocaleString()
          };
          history.value.unshift(historyItem);
          common_vendor.index.setStorageSync("website_snapshot_history", history.value);
          resolve();
        }, 2e3);
      });
    };
    const isValidUrl = (url2) => {
      if (!url2 || typeof url2 !== "string") {
        return false;
      }
      const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/i;
      if (!url2.startsWith("http://") && !url2.startsWith("https://")) {
        url2 = "http://" + url2;
      }
      return urlPattern.test(url2);
    };
    const loadFromHistory = (item) => {
      var _a;
      url.value = item.url;
      device.value = ((_a = devices.find((d) => d.name === item.device)) == null ? void 0 : _a.id) || "desktop";
      selectedFormat.value = item.format;
      previewImage.value = item.image;
      common_vendor.index.showToast({
        title: "已加载历史记录",
        icon: "success"
      });
    };
    const regenerateFromHistory = (item) => {
      loadFromHistory(item);
      handleGenerate();
    };
    const clearHistory = () => {
      history.value = [];
      common_vendor.index.showToast({
        title: "历史记录已清空",
        icon: "success"
      });
    };
    const downloadImage = () => {
      common_vendor.index.showToast({
        title: "开始下载图片",
        icon: "success"
      });
    };
    const shareImage = () => {
      common_vendor.index.showToast({
        title: "准备分享图片",
        icon: "success"
      });
    };
    const previewFullImage = () => {
      common_vendor.index.previewImage({
        urls: [previewImage.value],
        current: 0
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o([($event) => url.value = $event.detail.value, onUrlChange]),
        b: url.value,
        c: !url.value
      }, !url.value ? {
        d: common_vendor.o(pasteUrl)
      } : {}, {
        e: url.value
      }, url.value ? {
        f: common_vendor.o(clearUrl)
      } : {}, {
        g: common_vendor.o(validateUrl),
        h: common_vendor.f(devices, (deviceOption, k0, i0) => {
          return {
            a: common_vendor.t(deviceOption.icon),
            b: common_vendor.t(deviceOption.name),
            c: common_vendor.t(deviceOption.size),
            d: deviceOption.id,
            e: device.value === deviceOption.id ? 1 : "",
            f: common_vendor.o(($event) => selectDevice(deviceOption.id), deviceOption.id)
          };
        }),
        i: common_vendor.f(formats, (format, k0, i0) => {
          return {
            a: common_vendor.t(format.toUpperCase()),
            b: format,
            c: selectedFormat.value === format ? 1 : "",
            d: common_vendor.o(($event) => selectFormat(format), format)
          };
        }),
        j: common_vendor.t(quality.value),
        k: quality.value,
        l: common_vendor.o(onQualityChange),
        m: common_vendor.t(delay.value),
        n: delay.value,
        o: common_vendor.o(onDelayChange),
        p: fullPage.value,
        q: common_vendor.o(onFullPageChange),
        r: removeAds.value,
        s: common_vendor.o(onRemoveAdsChange),
        t: common_vendor.t(isGenerating.value ? "⏳" : "📸"),
        v: common_vendor.t(isGenerating.value ? "生成中..." : "生成网站快照"),
        w: isGenerating.value
      }, isGenerating.value ? {
        x: progress.value + "%"
      } : {}, {
        y: isGenerating.value ? 1 : "",
        z: !url.value ? 1 : "",
        A: common_vendor.o(handleGenerate),
        B: !url.value || isGenerating.value,
        C: previewImage.value
      }, previewImage.value ? {
        D: common_vendor.o(downloadImage),
        E: common_vendor.o(shareImage)
      } : {}, {
        F: !previewImage.value
      }, !previewImage.value ? {} : {
        G: previewImage.value,
        H: common_vendor.o(previewFullImage),
        I: common_vendor.t(deviceName.value),
        J: common_vendor.t(selectedFormat.value.toUpperCase()),
        K: common_vendor.t(quality.value)
      }, {
        L: history.value.length > 0
      }, history.value.length > 0 ? {
        M: common_vendor.o(clearHistory),
        N: common_vendor.f(history.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.url),
            b: common_vendor.t(item.device),
            c: common_vendor.t(item.format),
            d: common_vendor.t(item.time),
            e: common_vendor.o(($event) => regenerateFromHistory(item), index),
            f: index,
            g: common_vendor.o(($event) => loadFromHistory(item), index)
          };
        })
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c0a3279e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/website-snapshot.js.map
