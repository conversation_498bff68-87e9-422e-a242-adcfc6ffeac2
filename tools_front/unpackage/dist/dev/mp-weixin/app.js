"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/deals/deals.js";
  "./pages/profile/profile.js";
  "./pages/profile/card-exchange.js";
  "./pages/tool-detail/tool-detail.js";
  "./pages/tools/animation-code-generator.js";
  "./pages/tools/anime-wallpaper.js";
  "./pages/tools/answer-book.js";
  "./pages/tools/arrow-text.js";
  "./pages/tools/ascii-converter.js";
  "./pages/tools/audio-editor.js";
  "./pages/tools/beast-language.js";
  "./pages/tools/biased-wheel.js";
  "./pages/tools/bing-daily-wallpaper.js";
  "./pages/tools/black-white-converter.js";
  "./pages/tools/bmi-calculator.js";
  "./pages/tools/bordered-text.js";
  "./pages/tools/braid-nickname.js";
  "./pages/tools/sound-effects.js";
  "./pages/tools/case-converter.js";
  "./pages/tools/color-palette.js";
  "./pages/tools/color-scheme-generator.js";
  "./pages/tools/common-numbers.js";
  "./pages/tools/common-ports.js";
  "./pages/tools/confession-code-maker.js";
  "./pages/tools/contrast-checker.js";
  "./pages/tools/cpu-ladder.js";
  "./pages/tools/crazy-quotes.js";
  "./pages/tools/crazy-thursday-text.js";
  "./pages/tools/css-text-effects.js";
  "./pages/tools/dice-roller.js";
  "./pages/tools/eating-gif-avatar.js";
  "./pages/tools/emoji-collection.js";
  "./pages/tools/famous-quotes.js";
  "./pages/tools/flex-layout-generator.js";
  "./pages/tools/fullscreen-clock.js";
  "./pages/tools/fun-image-generator.js";
  "./pages/tools/game-voice-synthesis.js";
  "./pages/tools/glass-effect-generator.js";
  "./pages/tools/gpu-ladder.js";
  "./pages/tools/gradient-code-generator.js";
  "./pages/tools/gradient-palette.js";
  "./pages/tools/grid-layout-generator.js";
  "./pages/tools/handheld-barrage.js";
  "./pages/tools/heart-text.js";
  "./pages/tools/helicopter-text.js";
  "./pages/tools/hidden-image-maker.js";
  "./pages/tools/history-today.js";
  "./pages/tools/http-headers.js";
  "./pages/tools/http-status-codes.js";
  "./pages/tools/hue-generator.js";
  "./pages/tools/id-photo-maker.js";
  "./pages/tools/image-color-picker.js";
  "./pages/tools/image-compressor.js";
  "./pages/tools/image-mirror-flipper.js";
  "./pages/tools/image-mosaic-maker.js";
  "./pages/tools/image-pixelizer.js";
  "./pages/tools/image-stitcher.js";
  "./pages/tools/image-watermark-remover.js";
  "./pages/tools/image-watermarker.js";
  "./pages/tools/ip-signature.js";
  "./pages/tools/key-code-checker.js";
  "./pages/tools/life-counter.js";
  "./pages/tools/life-time.js";
  "./pages/tools/linux-commands.js";
  "./pages/tools/love-apartment-quotes.js";
  "./pages/tools/love-emoji.js";
  "./pages/tools/love-quotes.js";
  "./pages/tools/love-text-520.js";
  "./pages/tools/magic-watermark-remover.js";
  "./pages/tools/md5-encryptor.js";
  "./pages/tools/md5-modifier.js";
  "./pages/tools/mock-call.js";
  "./pages/tools/music-downloader.js";
  "./pages/tools/music-text.js";
  "./pages/tools/national-gas-price.js";
  "./pages/tools/national-weather-query.js";
  "./pages/tools/nine-grid-cutter.js";
  "./pages/tools/password-generator.js";
  "./pages/tools/permanent-membership.js";
  "./pages/tools/phone-cleaner.js";
  "./pages/tools/phone-detector.js";
  "./pages/tools/port-checker.js";
  "./pages/tools/portable-fan.js";
  "./pages/tools/px-em-converter.js";
  "./pages/tools/random-avatar.js";
  "./pages/tools/random-loading-image.js";
  "./pages/tools/random-number-generator.js";
  "./pages/tools/random-pc-wallpaper.js";
  "./pages/tools/rgb-hex-converter.js";
  "./pages/tools/sad-text-library.js";
  "./pages/tools/safe-period-calculator.js";
  "./pages/tools/scream-text.js";
  "./pages/tools/screenshot-frame.js";
  "./pages/tools/small-letter-nickname.js";
  "./pages/tools/special-tail.js";
  "./pages/tools/strike-through-text.js";
  "./pages/tools/subscript-phone.js";
  "./pages/tools/superscript-phone.js";
  "./pages/tools/tank-text.js";
  "./pages/tools/text-extractor.js";
  "./pages/tools/text-library.js";
  "./pages/tools/text-reverser.js";
  "./pages/tools/text-wallpaper.js";
  "./pages/tools/time-calculator.js";
  "./pages/tools/timestamp-converter.js";
  "./pages/tools/unicode-converter.js";
  "./pages/tools/user-agent-list.js";
  "./pages/tools/uuid-generator.js";
  "./pages/tools/vehicle-price-query.js";
  "./pages/tools/video-compressor.js";
  "./pages/tools/video-downloader.js";
  "./pages/tools/video-to-audio.js";
  "./pages/tools/video-watermark-remover.js";
  "./pages/tools/virtual-wooden-fish.js";
  "./pages/tools/website-snapshot.js";
  "./pages/tools/wifi-nickname.js";
  "./pages/profile/personal-info.js";
  "./pages/profile/membership.js";
  "./pages/profile/feedback.js";
  "./pages/profile/version.js";
  "./pages/not-found/not-found.js";
  "./pages/profile/permissions.js";
  "./pages/profile/clear-cache.js";
  "./pages/profile/follow-us.js";
  "./pages/profile/same-style.js";
  "./pages/tools/text-nine-grid.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:4", "App Launch");
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:7", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:10", "App Hide");
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.config.globalProperties.$baseUrl = "http://localhost:8080";
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
