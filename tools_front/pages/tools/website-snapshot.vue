<template>
  <view class="snapshot-container">
    <!-- 头部信息 -->
    <view class="header-card">
      <view class="header-content">
        <view class="header-icon">📸</view>
        <view class="header-info">
          <text class="header-title">网站截图生成器</text>
          <text class="header-subtitle">快速生成网站快照和预览图</text>
        </view>
      </view>
    </view>

      <!-- URL输入 -->
    <view class="input-card">
      <view class="card-header">
        <text class="card-title">🌐 网站地址</text>
      </view>
      
      <view class="input-content">
          <input
            v-model="url"
          type="text"
            placeholder="https://example.com"
          class="url-input"
          @input="onUrlChange"
        />
        <view class="url-actions">
          <button class="action-btn" @tap="pasteUrl" v-if="!url">
            <text class="btn-icon">📋</text>
            <text class="btn-text">粘贴</text>
          </button>
          <button class="action-btn" @tap="clearUrl" v-if="url">
            <text class="btn-icon">🗑️</text>
            <text class="btn-text">清空</text>
          </button>
          <button class="action-btn" @tap="validateUrl">
            <text class="btn-icon">✅</text>
            <text class="btn-text">验证</text>
          </button>
        </view>
      </view>
    </view>

      <!-- 设备选择 -->
    <view class="device-card">
      <view class="card-header">
        <text class="card-title">📱 设备类型</text>
      </view>
      
      <view class="device-grid">
        <view 
              v-for="deviceOption in devices"
              :key="deviceOption.id"
          class="device-item"
          :class="{ active: device === deviceOption.id }"
          @tap="selectDevice(deviceOption.id)"
        >
          <view class="device-icon">{{ deviceOption.icon }}</view>
          <view class="device-info">
            <text class="device-name">{{ deviceOption.name }}</text>
            <text class="device-size">{{ deviceOption.size }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 高级选项 -->
    <view class="options-card">
      <view class="card-header">
        <text class="card-title">⚙️ 高级选项</text>
      </view>
      
      <view class="options-content">
        <view class="option-group">
          <text class="option-label">图片格式</text>
          <view class="format-buttons">
            <button 
              v-for="format in formats" 
              :key="format"
              class="format-btn"
              :class="{ active: selectedFormat === format }"
              @tap="selectFormat(format)"
            >
              {{ format.toUpperCase() }}
            </button>
          </view>
        </view>

        <view class="option-group">
          <text class="option-label">图片质量 ({{ quality }}%)</text>
          <slider 
            :value="quality" 
            @change="onQualityChange"
            min="50" 
            max="100" 
            step="10"
            activeColor="#007AFF"
            backgroundColor="#e5e7eb"
            block-size="20"
            class="quality-slider"
          />
        </view>

        <view class="option-group">
          <text class="option-label">延迟时间 ({{ delay }}秒)</text>
          <slider 
            :value="delay" 
            @change="onDelayChange"
            min="0" 
            max="10" 
            step="1"
            activeColor="#007AFF"
            backgroundColor="#e5e7eb"
            block-size="20"
            class="delay-slider"
          />
        </view>

        <view class="option-switches">
          <view class="switch-item">
            <text class="switch-label">全页截图</text>
            <switch 
              :checked="fullPage" 
              @change="onFullPageChange"
              color="#007AFF"
            />
          </view>
          <view class="switch-item">
            <text class="switch-label">移除广告</text>
            <switch 
              :checked="removeAds" 
              @change="onRemoveAdsChange"
              color="#007AFF"
            />
          </view>
        </view>
      </view>
    </view>

      <!-- 生成按钮 -->
    <view class="generate-card">
          <button
        class="generate-btn"
        :class="{ generating: isGenerating, disabled: !url }"
            @tap="handleGenerate"
            :disabled="!url || isGenerating"
      >
        <view class="btn-content">
          <text class="btn-icon">{{ isGenerating ? '⏳' : '📸' }}</text>
          <text class="btn-text">{{ isGenerating ? '生成中...' : '生成网站快照' }}</text>
        </view>
        <view class="btn-progress" v-if="isGenerating" :style="{ width: progress + '%' }"></view>
      </button>
    </view>

    <!-- 快照预览 -->
    <view class="preview-card">
      <view class="card-header">
        <text class="card-title">👀 快照预览</text>
        <view class="preview-actions" v-if="previewImage">
          <button class="action-btn" @tap="downloadImage">
            <text class="btn-icon">💾</text>
            <text class="btn-text">下载</text>
          </button>
          <button class="action-btn" @tap="shareImage">
            <text class="btn-icon">📤</text>
            <text class="btn-text">分享</text>
          </button>
        </view>
      </view>

      <view class="preview-content">
        <view class="preview-container" v-if="!previewImage">
          <view class="preview-placeholder">
            <text class="placeholder-icon">📸</text>
            <text class="placeholder-text">输入网站地址并生成快照</text>
            <text class="placeholder-desc">支持桌面、平板、手机多种设备尺寸</text>
          </view>
        </view>
        
        <view class="preview-image" v-else>
          <image 
            :src="previewImage" 
            mode="aspectFit"
            class="snapshot-image"
            @tap="previewFullImage"
          />
          <view class="image-info">
            <text class="info-text">{{ deviceName }} • {{ selectedFormat.toUpperCase() }} • {{ quality }}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 历史记录 -->
    <view class="history-card" v-if="history.length > 0">
      <view class="card-header">
        <text class="card-title">📋 历史记录</text>
        <button class="action-btn" @tap="clearHistory">
          <text class="btn-icon">🗑️</text>
          <text class="btn-text">清空</text>
        </button>
      </view>
      
      <view class="history-list">
        <view 
          v-for="(item, index) in history" 
          :key="index"
          class="history-item"
          @tap="loadFromHistory(item)"
        >
          <view class="history-info">
            <text class="history-url">{{ item.url }}</text>
            <text class="history-details">{{ item.device }} • {{ item.format }} • {{ item.time }}</text>
          </view>
          <view class="history-actions">
            <button class="history-btn" @tap.stop="regenerateFromHistory(item)">
              <text class="btn-icon">🔄</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用说明 -->
    <view class="tips-card">
      <view class="card-header">
        <text class="card-title">💡 使用说明</text>
      </view>
      
      <view class="tips-content">
        <view class="tip-item">
          <text class="tip-title">🌐 输入网址</text>
          <text class="tip-desc">输入完整的网站URL地址，支持HTTP和HTTPS协议</text>
        </view>
        <view class="tip-item">
          <text class="tip-title">📱 选择设备</text>
          <text class="tip-desc">根据需要选择桌面、平板或手机设备尺寸</text>
        </view>
        <view class="tip-item">
          <text class="tip-title">⚙️ 调整选项</text>
          <text class="tip-desc">设置图片格式、质量和延迟时间等参数</text>
        </view>
        <view class="tip-item">
          <text class="tip-title">⚠️ 注意事项</text>
          <text class="tip-desc">某些网站可能限制截图，请确保网站可正常访问</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { toolService } from '../../utils/toolService.js'
import { showSuccess, showError, showLoading, hideLoading } from '../../utils/index.js'

const url = ref('')
const device = ref('desktop')
const selectedFormat = ref('png')
const quality = ref(90)
const delay = ref(2)
const fullPage = ref(false)
const removeAds = ref(false)
const isGenerating = ref(false)
const progress = ref(0)
const previewImage = ref('')
const history = ref([])

const devices = reactive([
  { id: 'desktop', name: '桌面端', size: '1920×1080', icon: '💻' },
  { id: 'tablet', name: '平板端', size: '768×1024', icon: '📱' },
  { id: 'mobile', name: '手机端', size: '375×667', icon: '📱' }
])

const formats = reactive(['png', 'jpg', 'webp'])

const deviceName = computed(() => {
  const device = devices.find(d => d.id === device.value)
  return device ? device.name : '桌面端'
})

onMounted(() => {
  const savedHistory = uni.getStorageSync('website_snapshot_history')
  if (savedHistory) {
    history.value = savedHistory
  }
})

const onUrlChange = (e) => {
  url.value = e.detail.value
}

const pasteUrl = async () => {
  try {
    const clipboardData = await uni.getClipboardData()
    if (clipboardData.data && isValidUrl(clipboardData.data)) {
      url.value = clipboardData.data
      uni.showToast({
        title: 'URL已粘贴',
        icon: 'success'
      })
    } else {
      uni.showToast({
        title: '剪贴板中无有效URL',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.showToast({
      title: '粘贴失败',
      icon: 'none'
    })
  }
}

const clearUrl = () => {
  url.value = ''
  uni.showToast({
    title: 'URL已清空',
    icon: 'success'
  })
}

const validateUrl = () => {
  if (isValidUrl(url.value)) {
    uni.showToast({
      title: 'URL格式正确',
      icon: 'success'
    })
  } else {
    uni.showToast({
      title: 'URL格式错误',
      icon: 'none'
    })
  }
}

const selectDevice = (deviceId) => {
  device.value = deviceId
  const device = devices.find(d => d.id === deviceId)
  uni.showToast({
    title: `已选择${device.name}`,
    icon: 'success'
  })
}

const selectFormat = (format) => {
  selectedFormat.value = format
  uni.showToast({
    title: `已选择${format.toUpperCase()}格式`,
    icon: 'success'
  })
}

const onQualityChange = (e) => {
  quality.value = e.detail.value
}

const onDelayChange = (e) => {
  delay.value = e.detail.value
}

const onFullPageChange = (e) => {
  fullPage.value = e.detail.value
}

const onRemoveAdsChange = (e) => {
  removeAds.value = e.detail.value
}

// 生成网站快照
const handleGenerate = async () => {
  if (!url.value || !isValidUrl(url.value)) {
    showError('请输入有效的网站地址')
    return
  }

  try {
    isGenerating.value = true
    progress.value = 0
    
    // 开始进度模拟
    const progressInterval = setInterval(() => {
      if (progress.value < 80) {
        progress.value += Math.random() * 20
      }
    }, 500)

    // 调用后端API
    const result = await toolService.generateWebsiteSnapshot({
      url: url.value,
      device: device.value,
      format: selectedFormat.value,
      quality: quality.value,
      delay: delay.value,
      fullPage: fullPage.value,
      removeAds: removeAds.value
    })

    clearInterval(progressInterval)
    progress.value = 100

    if (result.success) {
      previewImage.value = result.data.snapshotUrl
      showSuccess('网站快照生成成功！')
      
      // 添加到历史记录
      const historyItem = {
        id: Date.now(),
        url: url.value,
        image: result.data.snapshotUrl,
        device: deviceName.value,
        format: selectedFormat.value,
        timestamp: new Date().toLocaleString()
      }
      history.value.unshift(historyItem)
      
      // 保存到本地存储
      uni.setStorageSync('website_snapshot_history', history.value)
    } else {
      throw new Error(result.message || '生成失败')
    }
  } catch (error) {
    console.error('网站快照生成失败:', error)
    
    // 后端API失败时，使用本地模拟处理
    try {
      await simulateSnapshotGeneration()
      showSuccess('网站快照生成成功！（本地处理）')
    } catch (localError) {
      showError('网站快照生成失败，请重试')
    }
  } finally {
    isGenerating.value = false
    progress.value = 0
  }
}

// 本地模拟处理（后端API失败时的回退方案）
const simulateSnapshotGeneration = async () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟生成快照
      previewImage.value = `https://picsum.photos/800/600?random=${Date.now()}`
      
      // 添加到历史记录
      const historyItem = {
        id: Date.now(),
        url: url.value,
        image: previewImage.value,
        device: deviceName.value,
        format: selectedFormat.value,
        timestamp: new Date().toLocaleString()
      }
      history.value.unshift(historyItem)
      
      // 保存到本地存储
      uni.setStorageSync('website_snapshot_history', history.value)
      
      resolve()
    }, 2000)
  })
}

// URL验证
const isValidUrl = (url) => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

const loadFromHistory = (item) => {
  url.value = item.url
  device.value = devices.find(d => d.name === item.device)?.id || 'desktop'
  selectedFormat.value = item.format
  previewImage.value = item.image
  
  uni.showToast({
    title: '已加载历史记录',
    icon: 'success'
  })
}

const regenerateFromHistory = (item) => {
  loadFromHistory(item)
  handleGenerate()
}

const clearHistory = () => {
  history.value = []
  uni.showToast({
    title: '历史记录已清空',
    icon: 'success'
  })
}

const downloadImage = () => {
  uni.showToast({
    title: '开始下载图片',
    icon: 'success'
  })
  // 这里实现图片下载逻辑
}

const shareImage = () => {
  uni.showToast({
    title: '准备分享图片',
    icon: 'success'
  })
  // 这里实现图片分享逻辑
}

const previewFullImage = () => {
  uni.previewImage({
    urls: [previewImage.value],
    current: 0
  })
}
</script>

<style lang="scss" scoped>
.snapshot-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}

.header-card, .input-card, .device-card, .options-card, .generate-card, .preview-card, .history-card, .tips-card {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.header-content {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.header-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  color: #666;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.action-btn {
  height: 60rpx;
  padding: 0 28rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #007AFF;
  color: #fff;
  border: none;
  transition: background 0.2s, box-shadow 0.2s;
}

.action-btn:active {
  background: #2563eb;
}

.action-btn:disabled {
  background: #e5e7eb;
  color: #bdbdbd;
  cursor: not-allowed;
}

.input-content {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.url-input {
  flex: 1;
  min-width: 0;
  height: 60rpx;
  font-size: 28rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
  padding: 0 24rpx;
  background: #f8f9fa;
  color: #333;
  box-sizing: border-box;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.url-input:focus {
  border-color: #007AFF;
  box-shadow: 0 0 0 4rpx rgba(0,122,255,0.08);
  outline: none;
}

.url-input::placeholder {
  color: #bdbdbd;
}

.url-actions {
  display: flex;
  flex-direction: row;
  gap: 8rpx;
}

.device-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16rpx;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  background: white;
  transition: all 0.3s ease;
  
  &.active {
    border-color: #007AFF;
    background: #f0f8ff;
  }
  
  &:active {
    transform: scale(0.98);
  }
}

.device-icon {
  font-size: 36rpx;
  margin-right: 24rpx;
}

.device-info {
  flex: 1;
}

.device-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.device-size {
  font-size: 24rpx;
  color: #666;
}

.options-content {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.option-label {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.format-buttons {
  display: flex;
  gap: 12rpx;
}

.format-btn {
  padding: 12rpx 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  background: white;
  font-size: 22rpx;
  color: #666;
  transition: all 0.3s ease;
  
  &.active {
    border-color: #007AFF;
    background: #007AFF;
    color: white;
  }
}

.option-switches {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-label {
  font-size: 26rpx;
  color: #333;
}

.generate-btn {
  width: 100%;
  padding: 24rpx;
  border-radius: 12rpx;
  background: #007AFF;
  color: white;
  font-size: 28rpx;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &.generating {
    background: #4CAF50;
  }
  
  &.disabled {
    background: #ccc;
    color: #999;
  }
  
  &:active:not(.disabled) {
    transform: scale(0.98);
  }
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.btn-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.3s ease;
}

.preview-content {
  min-height: 400rpx;
}

.preview-container {
  height: 400rpx;
  border: 2rpx dashed #e5e5e5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.preview-placeholder {
  text-align: center;
  color: #999;
}

.placeholder-icon {
  font-size: 64rpx;
  display: block;
  margin-bottom: 16rpx;
}

.placeholder-text {
  font-size: 28rpx;
  display: block;
  margin-bottom: 8rpx;
}

.placeholder-desc {
  font-size: 24rpx;
  color: #ccc;
}

.preview-image {
  position: relative;
}

.snapshot-image {
  width: 100%;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.image-info {
  margin-top: 16rpx;
  text-align: center;
}

.info-text {
  font-size: 24rpx;
  color: #666;
}

.preview-actions {
  display: flex;
  gap: 8rpx;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  background: #fafafa;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
  }
}

.history-info {
  flex: 1;
}

.history-url {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  word-break: break-all;
}

.history-details {
  font-size: 22rpx;
  color: #666;
}

.history-actions {
  display: flex;
  gap: 8rpx;
}

.history-btn {
  padding: 8rpx 12rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 6rpx;
  background: white;
  font-size: 20rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.tip-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.tip-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}
</style>