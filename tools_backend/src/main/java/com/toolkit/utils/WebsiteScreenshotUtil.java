package com.toolkit.utils;

import com.microsoft.playwright.*;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.ScreenshotType;
import com.microsoft.playwright.options.ViewportSize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * 网站截图工具类
 */
@Slf4j
@Component
public class WebsiteScreenshotUtil {

    private static final List<String> AD_SELECTORS = Arrays.asList(
        "[id*='ad']", "[class*='ad']", "[id*='advertisement']", "[class*='advertisement']",
        "[id*='banner']", "[class*='banner']", "[id*='popup']", "[class*='popup']",
        ".ad", ".ads", ".advertisement", ".banner", ".popup", ".modal"
    );

    /**
     * 截取网站截图
     *
     * @param url 网站URL
     * @param width 截图宽度
     * @param height 截图高度
     * @param format 图片格式
     * @param quality 图片质量
     * @param delay 延迟时间(秒)
     * @param fullPage 是否全页截图
     * @param removeAds 是否移除广告
     * @param hideScrollbar 是否隐藏滚动条
     * @return 截图文件路径
     */
    public String captureWebsite(String url, int width, int height, String format,
                               int quality, int delay, boolean fullPage,
                               boolean removeAds, boolean hideScrollbar) {

        log.info("开始截图 - URL: {}, 尺寸: {}x{}, 格式: {}", url, width, height, format);

        Playwright playwright = null;
        Browser browser = null;
        BrowserContext context = null;
        Page page = null;

        try {
            // 创建 Playwright 实例
            log.info("创建 Playwright 实例...");
            playwright = Playwright.create();
            log.info("Playwright 实例创建成功");

            // 启动浏览器，使用最简单的配置
            log.info("启动浏览器...");
            browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
                .setHeadless(true)
                .setSlowMo(1000) // 添加1秒延迟，让操作更稳定
                .setArgs(Arrays.asList(
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-gpu",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--no-first-run",
                    "--disable-default-apps",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding"
                )));
            log.info("浏览器启动成功");

            // 创建浏览器上下文
            log.info("创建浏览器上下文...");
            context = browser.newContext(new Browser.NewContextOptions()
                .setViewportSize(width, height)
                .setUserAgent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"));
            log.info("浏览器上下文创建成功");

            // 创建新页面
            log.info("创建新页面...");
            page = context.newPage();
            log.info("新页面创建成功");

            // 设置超时时间
            page.setDefaultTimeout(30000); // 30秒
            page.setDefaultNavigationTimeout(30000); // 30秒

            // 导航到目标URL
            log.info("开始导航到目标URL: {}", url);
            try {
                // 如果是测试URL，使用本地HTML
                if (url.equals("test://local")) {
                    String testHtml = "<!DOCTYPE html><html><head><title>Test Page</title></head><body><h1>Hello World!</h1><p>This is a test page for screenshot.</p></body></html>";
                    page.setContent(testHtml);
                    log.info("设置测试HTML内容成功");
                } else {
                    // 使用更简单的导航方式
                    page.navigate(url, new Page.NavigateOptions().setTimeout(20000));
                    log.info("页面导航成功");

                    // 简单等待页面加载
                    try {
                        page.waitForLoadState(LoadState.LOAD, new Page.WaitForLoadStateOptions().setTimeout(10000));
                        log.info("页面加载完成");
                    } catch (Exception e) {
                        log.warn("等待页面加载超时，继续执行: {}", e.getMessage());
                    }
                }

            } catch (Exception e) {
                log.error("页面导航失败: {}", e.getMessage());
                throw new RuntimeException("无法访问目标网站: " + url, e);
            }

            // 延迟等待
            if (delay > 0) {
                Thread.sleep(delay * 1000L);
            }

            // 移除广告元素
            if (removeAds) {
                removeAdElements(page);
            }

            // 隐藏滚动条
            if (hideScrollbar) {
                hideScrollbars(page);
            }

            // 生成文件名
            String fileName = generateFileName(format);
            Path filePath = Paths.get("screenshots", fileName);

            // 确保目录存在
            filePath.getParent().toFile().mkdirs();

            // 截图选项
            Page.ScreenshotOptions screenshotOptions = new Page.ScreenshotOptions()
                .setPath(filePath)
                .setFullPage(fullPage)
                .setTimeout(30000); // 30秒截图超时

            // 设置图片格式和质量
            if ("jpg".equalsIgnoreCase(format) || "jpeg".equalsIgnoreCase(format)) {
                screenshotOptions.setType(ScreenshotType.JPEG);
                screenshotOptions.setQuality(quality);
            } else if ("png".equalsIgnoreCase(format)) {
                screenshotOptions.setType(ScreenshotType.PNG);
            }

            // 执行截图
            byte[] screenshot = page.screenshot(screenshotOptions);

            // 检查文件大小，如果太大则进行压缩
            screenshot = optimizeScreenshot(screenshot, format, quality);

            // 如果是webp格式，需要转换
            if ("webp".equalsIgnoreCase(format)) {
                convertToWebP(screenshot, filePath.toString());
            } else {
                // 直接写入文件
                java.nio.file.Files.write(filePath, screenshot);
            }

            long fileSize = screenshot.length;
            log.info("截图完成 - 文件: {}, 大小: {} KB", filePath.toString(), fileSize / 1024);

            // 如果文件仍然太大，记录警告
            if (fileSize > 10 * 1024 * 1024) { // 10MB
                log.warn("截图文件过大: {} MB，建议调整参数", fileSize / (1024 * 1024));
            }

            return filePath.toString();

        } catch (Exception e) {
            log.error("截图失败", e);
            throw new RuntimeException("截图失败: " + e.getMessage(), e);
        } finally {
            // 确保资源被正确释放
            try {
                if (page != null && !page.isClosed()) {
                    page.close();
                }
            } catch (Exception e) {
                log.warn("关闭页面失败: {}", e.getMessage());
            }

            try {
                if (context != null) {
                    context.close();
                }
            } catch (Exception e) {
                log.warn("关闭浏览器上下文失败: {}", e.getMessage());
            }

            try {
                if (browser != null) {
                    browser.close();
                }
            } catch (Exception e) {
                log.warn("关闭浏览器失败: {}", e.getMessage());
            }

            try {
                if (playwright != null) {
                    playwright.close();
                }
            } catch (Exception e) {
                log.warn("关闭Playwright失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 移除广告元素
     */
    private void removeAdElements(Page page) {
        try {
            for (String selector : AD_SELECTORS) {
                page.evaluate("() => {" +
                    "const elements = document.querySelectorAll('" + selector + "');" +
                    "elements.forEach(el => el.remove());" +
                "}");
            }
            log.debug("已移除广告元素");
        } catch (Exception e) {
            log.warn("移除广告元素失败: {}", e.getMessage());
        }
    }

    /**
     * 隐藏滚动条
     */
    private void hideScrollbars(Page page) {
        try {
            page.addStyleTag(new Page.AddStyleTagOptions().setContent(
                "* { scrollbar-width: none !important; -ms-overflow-style: none !important; }" +
                "*::-webkit-scrollbar { display: none !important; }"
            ));
            log.debug("已隐藏滚动条");
        } catch (Exception e) {
            log.warn("隐藏滚动条失败: {}", e.getMessage());
        }
    }

    /**
     * 优化截图大小
     */
    private byte[] optimizeScreenshot(byte[] originalData, String format, int quality) throws IOException {
        // 如果文件小于5MB，直接返回
        if (originalData.length < 5 * 1024 * 1024) {
            return originalData;
        }

        log.info("截图文件较大({} MB)，开始优化", originalData.length / (1024 * 1024));

        try {
            // 读取图片
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(originalData));
            if (image == null) {
                log.warn("无法读取图片数据，返回原始数据");
                return originalData;
            }

            // 如果图片尺寸过大，进行缩放
            int maxWidth = 1920;
            int maxHeight = 1080;

            if (image.getWidth() > maxWidth || image.getHeight() > maxHeight) {
                double scaleX = (double) maxWidth / image.getWidth();
                double scaleY = (double) maxHeight / image.getHeight();
                double scale = Math.min(scaleX, scaleY);

                int newWidth = (int) (image.getWidth() * scale);
                int newHeight = (int) (image.getHeight() * scale);

                BufferedImage scaledImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
                scaledImage.getGraphics().drawImage(image.getScaledInstance(newWidth, newHeight, java.awt.Image.SCALE_SMOOTH), 0, 0, null);
                image = scaledImage;

                log.info("图片已缩放至: {}x{}", newWidth, newHeight);
            }

            // 重新编码
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            if ("jpg".equalsIgnoreCase(format) || "jpeg".equalsIgnoreCase(format)) {
                // JPEG格式，使用质量压缩
                javax.imageio.ImageWriter writer = ImageIO.getImageWritersByFormatName("jpg").next();
                javax.imageio.ImageWriteParam param = writer.getDefaultWriteParam();
                param.setCompressionMode(javax.imageio.ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(Math.max(0.5f, quality / 100.0f)); // 最低50%质量

                writer.setOutput(ImageIO.createImageOutputStream(baos));
                writer.write(null, new javax.imageio.IIOImage(image, null, null), param);
                writer.dispose();
            } else {
                // PNG格式
                ImageIO.write(image, "png", baos);
            }

            byte[] optimizedData = baos.toByteArray();
            log.info("优化完成，文件大小从 {} KB 减少到 {} KB",
                    originalData.length / 1024, optimizedData.length / 1024);

            return optimizedData;

        } catch (Exception e) {
            log.error("图片优化失败，返回原始数据: {}", e.getMessage());
            return originalData;
        }
    }

    /**
     * 转换为WebP格式
     */
    private void convertToWebP(byte[] pngData, String outputPath) throws IOException {
        // 注意：这里需要额外的WebP编码库支持
        // 暂时保存为PNG格式，实际项目中可以集成WebP编码库
        log.warn("WebP格式转换暂未实现，保存为PNG格式");
        java.nio.file.Files.write(Paths.get(outputPath), pngData);
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String format) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return String.format("screenshot_%s.%s", timestamp, format.toLowerCase());
    }

    /**
     * 获取文件大小
     */
    public long getFileSize(String filePath) {
        try {
            return java.nio.file.Files.size(Paths.get(filePath));
        } catch (IOException e) {
            log.error("获取文件大小失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 验证URL格式
     */
    public boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        try {
            java.net.URL urlObj = new java.net.URL(url);
            return "http".equalsIgnoreCase(urlObj.getProtocol()) || 
                   "https".equalsIgnoreCase(urlObj.getProtocol());
        } catch (Exception e) {
            return false;
        }
    }
}
